from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
import atexit
import logging

from app.core.config import settings
from app.services.github_service import sync_all_repositories

# Set up logging
logger = logging.getLogger(__name__)

# Create scheduler
scheduler = BackgroundScheduler()

def sync_repositories_job():
    """
    Background job to sync repository data from GitHub
    """
    try:
        logger.info("Starting repository sync job")
        sync_all_repositories()
        logger.info("Repository sync job completed")
    except Exception as e:
        logger.error(f"Error in repository sync job: {str(e)}")

def init_scheduler():
    """
    Initialize and start the background scheduler
    """
    # Add jobs
    scheduler.add_job(
        func=sync_repositories_job,
        trigger=IntervalTrigger(minutes=settings.SCHEDULER_JOBS_INTERVAL),
        id='sync_repositories',
        name='Sync GitHub Repositories',
        replace_existing=True
    )
    
    # Start the scheduler
    scheduler.start()
    logger.info("Background scheduler started")
    
    # Shut down the scheduler when exiting the app
    atexit.register(lambda: scheduler.shutdown()) 