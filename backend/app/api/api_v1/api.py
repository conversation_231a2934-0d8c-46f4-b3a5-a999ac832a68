"""
API router configuration
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import repositories, auth, special_files, commits, milestones

api_router = APIRouter()

# Include routers from endpoints
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(repositories.router, prefix="/repositories", tags=["repositories"])
api_router.include_router(special_files.router, prefix="/special-files", tags=["special-files"])
api_router.include_router(commits.router, prefix="/commits", tags=["commits"])
api_router.include_router(milestones.router, prefix="/milestones", tags=["milestones"]) 