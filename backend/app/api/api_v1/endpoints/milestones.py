"""
API endpoints for repository milestones
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.core.database import get_db
from app.models.repository import Repository, Milestone
from app.schemas.milestone import MilestoneResponse, MilestoneCreate, MilestoneUpdate

router = APIRouter()


@router.get("/", response_model=List[MilestoneResponse])
async def get_milestones(
    repository_id: Optional[int] = None,
    is_completed: Optional[bool] = None,
    due_before: Optional[datetime] = None,
    due_after: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """
    Get all milestones with optional filtering
    """
    query = db.query(Milestone)
    
    if repository_id is not None:
        query = query.filter(Milestone.repository_id == repository_id)
    
    if is_completed is not None:
        query = query.filter(Milestone.is_completed == is_completed)
    
    if due_before is not None:
        query = query.filter(Milestone.due_date <= due_before)
    
    if due_after is not None:
        query = query.filter(Milestone.due_date >= due_after)
    
    # Filter out soft-deleted milestones unless explicitly included
    if not include_deleted:
        query = query.filter(Milestone.is_deleted == False)
    
    # Order by due date ascending (closest due date first)
    query = query.order_by(Milestone.due_date.asc())
    
    milestones = query.offset(skip).limit(limit).all()
    return milestones


@router.get("/{milestone_id}", response_model=MilestoneResponse)
async def get_milestone(
    milestone_id: int,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """
    Get a specific milestone by ID
    """
    query = db.query(Milestone).filter(Milestone.id == milestone_id)
    
    # Filter out soft-deleted milestones unless explicitly included
    if not include_deleted:
        query = query.filter(Milestone.is_deleted == False)
    
    milestone = query.first()
    
    if not milestone:
        raise HTTPException(status_code=404, detail="Milestone not found")
    
    return milestone


@router.post("/", response_model=MilestoneResponse, status_code=status.HTTP_201_CREATED)
async def create_milestone(
    milestone: MilestoneCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new milestone
    """
    # Check if repository exists
    repository = db.query(Repository).filter(
        Repository.id == milestone.repository_id,
        Repository.is_deleted == False
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    # Create new milestone
    db_milestone = Milestone(**milestone.dict())
    
    # Set completed_date if milestone is completed
    if db_milestone.is_completed and not db_milestone.completed_date:
        db_milestone.completed_date = datetime.utcnow()
    
    db.add(db_milestone)
    db.commit()
    db.refresh(db_milestone)
    
    return db_milestone


@router.put("/{milestone_id}", response_model=MilestoneResponse)
async def update_milestone(
    milestone_id: int,
    milestone: MilestoneUpdate,
    db: Session = Depends(get_db)
):
    """
    Update a milestone
    """
    db_milestone = db.query(Milestone).filter(
        Milestone.id == milestone_id,
        Milestone.is_deleted == False
    ).first()
    
    if not db_milestone:
        raise HTTPException(status_code=404, detail="Milestone not found")
    
    # Update milestone fields
    update_data = milestone.dict(exclude_unset=True)
    
    # Handle completion status change
    if "is_completed" in update_data and update_data["is_completed"] != db_milestone.is_completed:
        if update_data["is_completed"]:
            # Milestone was marked as completed
            update_data["completed_date"] = datetime.utcnow()
        else:
            # Milestone was marked as not completed
            update_data["completed_date"] = None
    
    for key, value in update_data.items():
        setattr(db_milestone, key, value)
    
    db.commit()
    db.refresh(db_milestone)
    
    return db_milestone


@router.delete("/{milestone_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_milestone(
    milestone_id: int,
    permanent: bool = False,
    db: Session = Depends(get_db)
):
    """
    Delete a milestone (soft delete by default)
    """
    db_milestone = db.query(Milestone).filter(
        Milestone.id == milestone_id,
        Milestone.is_deleted == False
    ).first()
    
    if not db_milestone:
        raise HTTPException(status_code=404, detail="Milestone not found")
    
    if permanent:
        # Hard delete
        db_milestone.hard_delete(db)
    else:
        # Soft delete
        db_milestone.soft_delete(db)


@router.post("/{milestone_id}/restore", response_model=MilestoneResponse)
async def restore_milestone(
    milestone_id: int,
    db: Session = Depends(get_db)
):
    """
    Restore a soft-deleted milestone
    """
    db_milestone = db.query(Milestone).filter(
        Milestone.id == milestone_id,
        Milestone.is_deleted == True
    ).first()
    
    if not db_milestone:
        raise HTTPException(status_code=404, detail="Milestone not found or not deleted")
    
    # Restore milestone
    db_milestone.is_deleted = False
    db_milestone.deleted_at = None
    db.commit()
    db.refresh(db_milestone)
    
    return db_milestone


@router.post("/{milestone_id}/complete", response_model=MilestoneResponse)
async def complete_milestone(
    milestone_id: int,
    db: Session = Depends(get_db)
):
    """
    Mark a milestone as completed
    """
    db_milestone = db.query(Milestone).filter(
        Milestone.id == milestone_id,
        Milestone.is_deleted == False
    ).first()
    
    if not db_milestone:
        raise HTTPException(status_code=404, detail="Milestone not found")
    
    # Mark as completed
    db_milestone.is_completed = True
    db_milestone.completed_date = datetime.utcnow()
    db.commit()
    db.refresh(db_milestone)
    
    return db_milestone


@router.post("/{milestone_id}/reopen", response_model=MilestoneResponse)
async def reopen_milestone(
    milestone_id: int,
    db: Session = Depends(get_db)
):
    """
    Reopen a completed milestone
    """
    db_milestone = db.query(Milestone).filter(
        Milestone.id == milestone_id,
        Milestone.is_deleted == False
    ).first()
    
    if not db_milestone:
        raise HTTPException(status_code=404, detail="Milestone not found")
    
    # Mark as not completed
    db_milestone.is_completed = False
    db_milestone.completed_date = None
    db.commit()
    db.refresh(db_milestone)
    
    return db_milestone 