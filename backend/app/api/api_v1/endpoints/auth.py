"""
API endpoints for authentication
"""
from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status, Request
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
import httpx
import secrets
from datetime import datetime, timedelta

from app.core.config import settings
from app.core.database import get_db
from app.core.security import create_access_token, get_current_user
from app.models.user import User
from app.schemas.auth import Token, TokenPayload
from app.schemas.user import UserCreate, UserResponse

router = APIRouter()

# Store state tokens temporarily for OAuth flow
oauth_states = {}


@router.get("/github/login")
async def github_login():
    """
    Initiate GitHub OAuth login flow
    """
    # Generate a random state token to prevent CSRF
    state = secrets.token_urlsafe(32)
    oauth_states[state] = datetime.utcnow()
    
    # Clean up expired states
    expired = datetime.utcnow() - timedelta(minutes=10)
    for s in list(oauth_states.keys()):
        if oauth_states[s] < expired:
            oauth_states.pop(s)
    
    # Redirect to GitHub OAuth authorization URL
    github_auth_url = (
        f"https://github.com/login/oauth/authorize"
        f"?client_id={settings.GITHUB_CLIENT_ID}"
        f"&redirect_uri={settings.FRONTEND_URL}/auth/callback"
        f"&scope=repo,read:user,user:email"
        f"&state={state}"
    )
    return RedirectResponse(url=github_auth_url)


@router.get("/github/callback")
async def github_callback(
    code: str,
    state: str,
    db: Session = Depends(get_db)
):
    """
    Handle GitHub OAuth callback
    """
    # Verify state token
    if state not in oauth_states:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid state parameter"
        )
    
    # Remove used state token
    oauth_states.pop(state)
    
    # Exchange code for access token
    token_url = "https://github.com/login/oauth/access_token"
    token_data = {
        "client_id": settings.GITHUB_CLIENT_ID,
        "client_secret": settings.GITHUB_CLIENT_SECRET,
        "code": code,
        "redirect_uri": f"{settings.FRONTEND_URL}/auth/callback"
    }
    headers = {"Accept": "application/json"}
    
    async with httpx.AsyncClient() as client:
        token_response = await client.post(token_url, json=token_data, headers=headers)
        
        if token_response.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Could not retrieve GitHub access token"
            )
        
        token_json = token_response.json()
        github_token = token_json.get("access_token")
        
        if not github_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="GitHub access token not found in response"
            )
        
        # Get user info from GitHub
        user_url = "https://api.github.com/user"
        user_headers = {
            "Authorization": f"token {github_token}",
            "Accept": "application/json"
        }
        
        user_response = await client.get(user_url, headers=user_headers)
        
        if user_response.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Could not retrieve GitHub user info"
            )
        
        user_data = user_response.json()
        
        # Get user emails from GitHub
        emails_url = "https://api.github.com/user/emails"
        emails_response = await client.get(emails_url, headers=user_headers)
        
        if emails_response.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Could not retrieve GitHub user emails"
            )
        
        emails_data = emails_response.json()
        primary_email = next((email["email"] for email in emails_data if email["primary"]), None)
        
        if not primary_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No primary email found in GitHub account"
            )
        
        # Check if user exists in database
        db_user = db.query(User).filter(User.github_id == str(user_data["id"])).first()
        
        if not db_user:
            # Create new user
            user_create = UserCreate(
                username=user_data["login"],
                email=primary_email,
                github_id=str(user_data["id"]),
                github_token=github_token
            )
            
            db_user = User(
                username=user_create.username,
                email=user_create.email,
                github_id=user_create.github_id,
                github_token=user_create.github_token
            )
            
            db.add(db_user)
            db.commit()
            db.refresh(db_user)
        else:
            # Update existing user's token
            db_user.github_token = github_token
            db_user.last_login = datetime.utcnow()
            db.commit()
            db.refresh(db_user)
        
        # Create access token
        access_token = create_access_token(
            data={"sub": db_user.id}
        )
        
        # Redirect to frontend with token
        return RedirectResponse(
            url=f"{settings.FRONTEND_URL}/auth/success?token={access_token}"
        )


@router.post("/token", response_model=Token)
async def login_for_access_token(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Get access token from GitHub token (for testing/development)
    """
    auth_header = request.headers.get("Authorization")
    
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    github_token = auth_header.split(" ")[1]
    
    async with httpx.AsyncClient() as client:
        # Get user info from GitHub
        user_url = "https://api.github.com/user"
        user_headers = {
            "Authorization": f"token {github_token}",
            "Accept": "application/json"
        }
        
        user_response = await client.get(user_url, headers=user_headers)
        
        if user_response.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid GitHub token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user_data = user_response.json()
        
        # Check if user exists in database
        db_user = db.query(User).filter(User.github_id == str(user_data["id"])).first()
        
        if not db_user:
            # Get user emails from GitHub
            emails_url = "https://api.github.com/user/emails"
            emails_response = await client.get(emails_url, headers=user_headers)
            
            if emails_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Could not retrieve GitHub user emails"
                )
            
            emails_data = emails_response.json()
            primary_email = next((email["email"] for email in emails_data if email["primary"]), None)
            
            if not primary_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No primary email found in GitHub account"
                )
            
            # Create new user
            db_user = User(
                username=user_data["login"],
                email=primary_email,
                github_id=str(user_data["id"]),
                github_token=github_token
            )
            
            db.add(db_user)
            db.commit()
            db.refresh(db_user)
        else:
            # Update existing user's token
            db_user.github_token = github_token
            db_user.last_login = datetime.utcnow()
            db.commit()
            db.refresh(db_user)
        
        # Create access token
        access_token = create_access_token(
            data={"sub": db_user.id}
        )
        
        return {"access_token": access_token, "token_type": "bearer"}


@router.get("/me", response_model=UserResponse)
async def read_users_me(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user information
    """
    return current_user 