"""
API endpoints for repository management
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.repository import Repository
from app.schemas.repository import RepositoryCreate, RepositoryUpdate, RepositoryResponse, RepositoryDetail
from app.services.github_service import GitHubService

router = APIRouter()


@router.get("/", response_model=List[RepositoryResponse])
async def get_repositories(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """
    Get all repositories with optional filtering
    """
    query = db.query(Repository)
    
    if is_active is not None:
        query = query.filter(Repository.is_active == is_active)
    
    # Filter out soft-deleted repositories unless explicitly included
    if not include_deleted:
        query = query.filter(Repository.is_deleted == False)
    
    repositories = query.offset(skip).limit(limit).all()
    return repositories


@router.get("/{repository_id}", response_model=RepositoryDetail)
async def get_repository(
    repository_id: int,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """
    Get a specific repository by ID with detailed information
    """
    query = db.query(Repository).filter(Repository.id == repository_id)
    
    # Filter out soft-deleted repositories unless explicitly included
    if not include_deleted:
        query = query.filter(Repository.is_deleted == False)
    
    repository = query.first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    return repository


@router.post("/", response_model=RepositoryResponse, status_code=status.HTTP_201_CREATED)
async def create_repository(
    repository: RepositoryCreate,
    db: Session = Depends(get_db)
):
    """
    Add a new repository to track
    """
    # Check if repository already exists (including soft-deleted ones)
    existing_repo = db.query(Repository).filter(Repository.full_name == repository.full_name).first()
    
    if existing_repo:
        if existing_repo.is_deleted:
            # If repository was soft-deleted, restore it
            existing_repo.is_deleted = False
            existing_repo.deleted_at = None
            db.commit()
            db.refresh(existing_repo)
            
            # Sync with GitHub to get latest data
            github_service = GitHubService(db)
            updated_repo = github_service.sync_repository(existing_repo.full_name)
            return updated_repo
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Repository {repository.full_name} is already being tracked"
            )
    
    # Fetch repository data from GitHub
    try:
        github_service = GitHubService(db)
        db_repo = github_service.sync_repository(repository.full_name)
        return db_repo
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Error fetching repository from GitHub: {str(e)}"
        )


@router.put("/{repository_id}", response_model=RepositoryResponse)
async def update_repository(
    repository_id: int,
    repository: RepositoryUpdate,
    db: Session = Depends(get_db)
):
    """
    Update repository settings
    """
    db_repo = db.query(Repository).filter(
        Repository.id == repository_id,
        Repository.is_deleted == False
    ).first()
    
    if not db_repo:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    # Update repository fields
    update_data = repository.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_repo, key, value)
    
    db.commit()
    db.refresh(db_repo)
    return db_repo


@router.delete("/{repository_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_repository(
    repository_id: int,
    permanent: bool = False,
    db: Session = Depends(get_db)
):
    """
    Remove a repository from tracking (soft delete by default)
    """
    db_repo = db.query(Repository).filter(
        Repository.id == repository_id,
        Repository.is_deleted == False
    ).first()
    
    if not db_repo:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    if permanent:
        # Hard delete
        db_repo.hard_delete(db)
    else:
        # Soft delete
        db_repo.soft_delete(db)


@router.post("/{repository_id}/restore", response_model=RepositoryResponse)
async def restore_repository(
    repository_id: int,
    db: Session = Depends(get_db)
):
    """
    Restore a soft-deleted repository
    """
    db_repo = db.query(Repository).filter(
        Repository.id == repository_id,
        Repository.is_deleted == True
    ).first()
    
    if not db_repo:
        raise HTTPException(status_code=404, detail="Repository not found or not deleted")
    
    # Restore repository
    db_repo.is_deleted = False
    db_repo.deleted_at = None
    db.commit()
    db.refresh(db_repo)
    
    return db_repo


@router.post("/{repository_id}/sync", response_model=RepositoryResponse)
async def sync_repository(
    repository_id: int,
    db: Session = Depends(get_db)
):
    """
    Manually sync repository data from GitHub
    """
    db_repo = db.query(Repository).filter(
        Repository.id == repository_id,
        Repository.is_deleted == False
    ).first()
    
    if not db_repo:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    try:
        github_service = GitHubService(db)
        updated_repo = github_service.sync_repository(db_repo.full_name)
        return updated_repo
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Error syncing repository: {str(e)}"
        ) 