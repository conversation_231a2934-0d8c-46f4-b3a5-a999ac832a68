"""
API endpoints for repository commits
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.core.database import get_db
from app.models.repository import Repository, Commit
from app.schemas.commit import CommitResponse, CommitCreate, CommitUpdate
from app.services.github_service import GitHubService

router = APIRouter()


@router.get("/", response_model=List[CommitResponse])
async def get_commits(
    repository_id: Optional[int] = None,
    author: Optional[str] = None,
    since: Optional[datetime] = None,
    until: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """
    Get all commits with optional filtering
    """
    query = db.query(Commit)
    
    if repository_id is not None:
        query = query.filter(Commit.repository_id == repository_id)
    
    if author is not None:
        query = query.filter(Commit.author == author)
    
    if since is not None:
        query = query.filter(Commit.committed_date >= since)
    
    if until is not None:
        query = query.filter(Commit.committed_date <= until)
    
    # Filter out soft-deleted commits unless explicitly included
    if not include_deleted:
        query = query.filter(Commit.is_deleted == False)
    
    # Order by committed date descending (newest first)
    query = query.order_by(Commit.committed_date.desc())
    
    commits = query.offset(skip).limit(limit).all()
    return commits


@router.get("/{commit_id}", response_model=CommitResponse)
async def get_commit(
    commit_id: int,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """
    Get a specific commit by ID
    """
    query = db.query(Commit).filter(Commit.id == commit_id)
    
    # Filter out soft-deleted commits unless explicitly included
    if not include_deleted:
        query = query.filter(Commit.is_deleted == False)
    
    commit = query.first()
    
    if not commit:
        raise HTTPException(status_code=404, detail="Commit not found")
    
    return commit


@router.post("/", response_model=CommitResponse, status_code=status.HTTP_201_CREATED)
async def create_commit(
    commit: CommitCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new commit (manual entry)
    """
    # Check if repository exists
    repository = db.query(Repository).filter(
        Repository.id == commit.repository_id,
        Repository.is_deleted == False
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    # Check if commit already exists
    existing_commit = db.query(Commit).filter(
        Commit.repository_id == commit.repository_id,
        Commit.sha == commit.sha,
        Commit.is_deleted == False
    ).first()
    
    if existing_commit:
        raise HTTPException(
            status_code=400,
            detail=f"Commit with SHA {commit.sha} already exists for this repository"
        )
    
    # Create new commit
    db_commit = Commit(**commit.dict())
    db.add(db_commit)
    db.commit()
    db.refresh(db_commit)
    
    return db_commit


@router.put("/{commit_id}", response_model=CommitResponse)
async def update_commit(
    commit_id: int,
    commit: CommitUpdate,
    db: Session = Depends(get_db)
):
    """
    Update a commit (manual update)
    """
    db_commit = db.query(Commit).filter(
        Commit.id == commit_id,
        Commit.is_deleted == False
    ).first()
    
    if not db_commit:
        raise HTTPException(status_code=404, detail="Commit not found")
    
    # Update commit fields
    update_data = commit.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_commit, key, value)
    
    db.commit()
    db.refresh(db_commit)
    
    return db_commit


@router.delete("/{commit_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_commit(
    commit_id: int,
    permanent: bool = False,
    db: Session = Depends(get_db)
):
    """
    Delete a commit (soft delete by default)
    """
    db_commit = db.query(Commit).filter(
        Commit.id == commit_id,
        Commit.is_deleted == False
    ).first()
    
    if not db_commit:
        raise HTTPException(status_code=404, detail="Commit not found")
    
    if permanent:
        # Hard delete
        db_commit.hard_delete(db)
    else:
        # Soft delete
        db_commit.soft_delete(db)


@router.post("/{commit_id}/restore", response_model=CommitResponse)
async def restore_commit(
    commit_id: int,
    db: Session = Depends(get_db)
):
    """
    Restore a soft-deleted commit
    """
    db_commit = db.query(Commit).filter(
        Commit.id == commit_id,
        Commit.is_deleted == True
    ).first()
    
    if not db_commit:
        raise HTTPException(status_code=404, detail="Commit not found or not deleted")
    
    # Restore commit
    db_commit.is_deleted = False
    db_commit.deleted_at = None
    db.commit()
    db.refresh(db_commit)
    
    return db_commit


@router.post("/sync/{repository_id}", response_model=List[CommitResponse])
async def sync_commits(
    repository_id: int,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Sync commits from GitHub for a repository
    """
    # Check if repository exists
    repository = db.query(Repository).filter(
        Repository.id == repository_id,
        Repository.is_deleted == False
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    try:
        # Sync commits from GitHub
        github_service = GitHubService(db)
        github_service._sync_commits(repository, limit=limit)
        
        # Get updated commits
        commits = db.query(Commit).filter(
            Commit.repository_id == repository_id,
            Commit.is_deleted == False
        ).order_by(Commit.committed_date.desc()).limit(limit).all()
        
        return commits
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Error syncing commits: {str(e)}"
        ) 