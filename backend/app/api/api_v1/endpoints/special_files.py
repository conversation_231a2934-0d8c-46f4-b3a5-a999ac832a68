"""
API endpoints for special configuration files (.dockerwrapper, .claude, .cursor)
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.repository import Repository, SpecialFile
from app.schemas.special_file import SpecialFileResponse, SpecialFileCreate, SpecialFileUpdate
from app.services.github_service import GitHubService

router = APIRouter()


@router.get("/", response_model=List[SpecialFileResponse])
async def get_special_files(
    repository_id: Optional[int] = None,
    file_type: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """
    Get all special configuration files with optional filtering
    """
    query = db.query(SpecialFile)
    
    if repository_id is not None:
        query = query.filter(SpecialFile.repository_id == repository_id)
    
    if file_type is not None:
        query = query.filter(SpecialFile.file_type == file_type)
    
    # Filter out soft-deleted files unless explicitly included
    if not include_deleted:
        query = query.filter(SpecialFile.is_deleted == False)
    
    special_files = query.offset(skip).limit(limit).all()
    return special_files


@router.get("/{special_file_id}", response_model=SpecialFileResponse)
async def get_special_file(
    special_file_id: int,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """
    Get a specific special configuration file by ID
    """
    query = db.query(SpecialFile).filter(SpecialFile.id == special_file_id)
    
    # Filter out soft-deleted files unless explicitly included
    if not include_deleted:
        query = query.filter(SpecialFile.is_deleted == False)
    
    special_file = query.first()
    
    if not special_file:
        raise HTTPException(status_code=404, detail="Special file not found")
    
    return special_file


@router.post("/", response_model=SpecialFileResponse, status_code=status.HTTP_201_CREATED)
async def create_special_file(
    special_file: SpecialFileCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new special configuration file
    """
    # Check if repository exists
    repository = db.query(Repository).filter(
        Repository.id == special_file.repository_id,
        Repository.is_deleted == False
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    # Check if special file already exists
    existing_file = db.query(SpecialFile).filter(
        SpecialFile.repository_id == special_file.repository_id,
        SpecialFile.file_type == special_file.file_type,
        SpecialFile.is_deleted == False
    ).first()
    
    if existing_file:
        raise HTTPException(
            status_code=400,
            detail=f"Special file of type {special_file.file_type} already exists for this repository"
        )
    
    # Create new special file
    db_special_file = SpecialFile(**special_file.dict())
    db.add(db_special_file)
    db.commit()
    db.refresh(db_special_file)
    
    return db_special_file


@router.put("/{special_file_id}", response_model=SpecialFileResponse)
async def update_special_file(
    special_file_id: int,
    special_file: SpecialFileUpdate,
    db: Session = Depends(get_db)
):
    """
    Update a special configuration file
    """
    db_special_file = db.query(SpecialFile).filter(
        SpecialFile.id == special_file_id,
        SpecialFile.is_deleted == False
    ).first()
    
    if not db_special_file:
        raise HTTPException(status_code=404, detail="Special file not found")
    
    # Update special file fields
    update_data = special_file.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_special_file, key, value)
    
    db.commit()
    db.refresh(db_special_file)
    
    return db_special_file


@router.delete("/{special_file_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_special_file(
    special_file_id: int,
    permanent: bool = False,
    db: Session = Depends(get_db)
):
    """
    Delete a special configuration file (soft delete by default)
    """
    db_special_file = db.query(SpecialFile).filter(
        SpecialFile.id == special_file_id,
        SpecialFile.is_deleted == False
    ).first()
    
    if not db_special_file:
        raise HTTPException(status_code=404, detail="Special file not found")
    
    if permanent:
        # Hard delete
        db_special_file.hard_delete(db)
    else:
        # Soft delete
        db_special_file.soft_delete(db)


@router.post("/{special_file_id}/restore", response_model=SpecialFileResponse)
async def restore_special_file(
    special_file_id: int,
    db: Session = Depends(get_db)
):
    """
    Restore a soft-deleted special configuration file
    """
    db_special_file = db.query(SpecialFile).filter(
        SpecialFile.id == special_file_id,
        SpecialFile.is_deleted == True
    ).first()
    
    if not db_special_file:
        raise HTTPException(status_code=404, detail="Special file not found or not deleted")
    
    # Restore special file
    db_special_file.is_deleted = False
    db_special_file.deleted_at = None
    db.commit()
    db.refresh(db_special_file)
    
    return db_special_file


@router.post("/sync/{repository_id}", response_model=List[SpecialFileResponse])
async def sync_special_files(
    repository_id: int,
    db: Session = Depends(get_db)
):
    """
    Sync special configuration files from GitHub for a repository
    """
    # Check if repository exists
    repository = db.query(Repository).filter(
        Repository.id == repository_id,
        Repository.is_deleted == False
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    try:
        # Sync special files from GitHub
        github_service = GitHubService(db)
        github_service._sync_special_files(repository)
        
        # Get updated special files
        special_files = db.query(SpecialFile).filter(
            SpecialFile.repository_id == repository_id,
            SpecialFile.is_deleted == False
        ).all()
        
        return special_files
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Error syncing special files: {str(e)}"
        ) 