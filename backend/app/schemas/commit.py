"""
Pydantic schemas for repository commits
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class CommitBase(BaseModel):
    """Base schema for repository commits"""
    sha: str = Field(..., description="Commit SHA hash")
    message: str = Field(..., description="Commit message")
    author: str = Field(..., description="Commit author name")
    author_email: Optional[str] = Field(None, description="Commit author email")
    committed_date: datetime = Field(..., description="Date when the commit was made")


class CommitCreate(CommitBase):
    """Schema for creating a new commit"""
    repository_id: int = Field(..., description="ID of the repository this commit belongs to")


class CommitUpdate(BaseModel):
    """Schema for updating a commit"""
    message: Optional[str] = Field(None, description="Commit message")
    author: Optional[str] = Field(None, description="Commit author name")
    author_email: Optional[str] = Field(None, description="Commit author email")
    committed_date: Optional[datetime] = Field(None, description="Date when the commit was made")


class CommitResponse(CommitBase):
    """Response schema for commits"""
    id: int
    repository_id: int
    created_at: datetime
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None

    class Config:
        orm_mode = True 