"""
Pydantic schemas for repository milestones
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class MilestoneBase(BaseModel):
    """Base schema for repository milestones"""
    title: str = Field(..., description="Milestone title")
    description: Optional[str] = Field(None, description="Milestone description")
    due_date: Optional[datetime] = Field(None, description="Due date for the milestone")
    is_completed: bool = Field(False, description="Whether the milestone is completed")


class MilestoneCreate(MilestoneBase):
    """Schema for creating a new milestone"""
    repository_id: int = Field(..., description="ID of the repository this milestone belongs to")
    completed_date: Optional[datetime] = Field(None, description="Date when the milestone was completed")


class MilestoneUpdate(BaseModel):
    """Schema for updating a milestone"""
    title: Optional[str] = Field(None, description="Milestone title")
    description: Optional[str] = Field(None, description="Milestone description")
    due_date: Optional[datetime] = Field(None, description="Due date for the milestone")
    is_completed: Optional[bool] = Field(None, description="Whether the milestone is completed")
    completed_date: Optional[datetime] = Field(None, description="Date when the milestone was completed")


class MilestoneResponse(MilestoneBase):
    """Response schema for milestones"""
    id: int
    repository_id: int
    completed_date: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None

    class Config:
        orm_mode = True 