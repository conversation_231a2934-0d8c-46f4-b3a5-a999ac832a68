"""
Pydantic schemas for special configuration files
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class SpecialFileBase(BaseModel):
    """Base schema for special configuration files"""
    file_type: str = Field(..., description="Type of special file (dockerwrapper, claude, cursor)")
    file_path: str = Field(..., description="Path to the file in the repository")
    content: Optional[str] = Field(None, description="Content of the file")


class SpecialFileCreate(SpecialFileBase):
    """Schema for creating a new special file"""
    repository_id: int = Field(..., description="ID of the repository this file belongs to")


class SpecialFileUpdate(BaseModel):
    """Schema for updating a special file"""
    file_path: Optional[str] = Field(None, description="Path to the file in the repository")
    content: Optional[str] = Field(None, description="Content of the file")


class SpecialFileResponse(SpecialFileBase):
    """Response schema for special files"""
    id: int
    repository_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None

    class Config:
        orm_mode = True 