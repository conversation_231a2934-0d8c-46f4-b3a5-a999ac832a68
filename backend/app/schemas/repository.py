"""
Pydantic schemas for repository data
"""
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, HttpUrl, Field


class SpecialFileBase(BaseModel):
    """Base schema for special configuration files"""
    file_type: str
    file_path: str
    content: Optional[str] = None


class SpecialFileResponse(SpecialFileBase):
    """Response schema for special configuration files"""
    id: int
    repository_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class CommitBase(BaseModel):
    """Base schema for repository commits"""
    sha: str
    message: str
    author: str
    author_email: Optional[str] = None
    committed_date: datetime


class CommitResponse(CommitBase):
    """Response schema for repository commits"""
    id: int
    repository_id: int
    created_at: datetime

    class Config:
        orm_mode = True


class MilestoneBase(BaseModel):
    """Base schema for repository milestones"""
    title: str
    description: Optional[str] = None
    due_date: Optional[datetime] = None
    is_completed: bool = False
    completed_date: Optional[datetime] = None


class MilestoneCreate(MilestoneBase):
    """Schema for creating a new milestone"""
    repository_id: int


class MilestoneUpdate(BaseModel):
    """Schema for updating a milestone"""
    title: Optional[str] = None
    description: Optional[str] = None
    due_date: Optional[datetime] = None
    is_completed: Optional[bool] = None
    completed_date: Optional[datetime] = None


class MilestoneResponse(MilestoneBase):
    """Response schema for repository milestones"""
    id: int
    repository_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class RepositoryBase(BaseModel):
    """Base schema for repository data"""
    name: str
    full_name: str = Field(..., description="Repository full name in format owner/repo")
    description: Optional[str] = None
    is_active: bool = True


class RepositoryCreate(BaseModel):
    """Schema for creating a new repository"""
    full_name: str = Field(..., description="Repository full name in format owner/repo")


class RepositoryUpdate(BaseModel):
    """Schema for updating a repository"""
    description: Optional[str] = None
    is_active: Optional[bool] = None


class RepositoryResponse(RepositoryBase):
    """Basic response schema for repositories"""
    id: int
    url: str
    html_url: str
    clone_url: str
    default_branch: str
    stars: int
    forks: int
    open_issues: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_synced_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class RepositoryDetail(RepositoryResponse):
    """Detailed response schema for repositories including related data"""
    special_files: List[SpecialFileResponse] = []
    commits: List[CommitResponse] = []
    milestones: List[MilestoneResponse] = []

    class Config:
        orm_mode = True 