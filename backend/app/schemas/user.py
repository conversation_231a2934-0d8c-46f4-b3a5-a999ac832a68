"""
Pydantic schemas for user data
"""
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    """Base schema for user data"""
    username: str
    email: EmailStr
    is_active: bool = True


class UserCreate(UserBase):
    """Schema for creating a new user"""
    github_id: str
    github_token: str


class UserUpdate(BaseModel):
    """Schema for updating a user"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None


class UserResponse(UserBase):
    """Response schema for user data"""
    id: int
    github_id: str
    is_superuser: bool
    created_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        orm_mode = True


class NotificationBase(BaseModel):
    """Base schema for notifications"""
    title: str
    content: str
    is_read: bool = False


class NotificationCreate(NotificationBase):
    """Schema for creating a notification"""
    user_id: int
    repository_id: Optional[int] = None


class NotificationUpdate(BaseModel):
    """Schema for updating a notification"""
    is_read: Optional[bool] = None


class NotificationResponse(NotificationBase):
    """Response schema for notifications"""
    id: int
    user_id: int
    repository_id: Optional[int] = None
    created_at: datetime

    class Config:
        orm_mode = True


class UserPreferenceBase(BaseModel):
    """Base schema for user preferences"""
    theme: str = "light"
    email_notifications: bool = True
    dashboard_layout: Optional[str] = None


class UserPreferenceCreate(UserPreferenceBase):
    """Schema for creating user preferences"""
    user_id: int


class UserPreferenceUpdate(BaseModel):
    """Schema for updating user preferences"""
    theme: Optional[str] = None
    email_notifications: Optional[bool] = None
    dashboard_layout: Optional[str] = None


class UserPreferenceResponse(UserPreferenceBase):
    """Response schema for user preferences"""
    id: int
    user_id: int
    updated_at: datetime

    class Config:
        orm_mode = True 