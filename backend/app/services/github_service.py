"""
GitHub API service for interacting with repositories
"""
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any

from github import Github, GithubException
from sqlalchemy.orm import Session

from app.core.config import settings
from app.models.repository import Repository, SpecialFile, Commit

logger = logging.getLogger(__name__)

class GitHubService:
    """Service for interacting with GitHub API"""
    
    def __init__(self, db: Session):
        """Initialize GitHub client with credentials"""
        self.github = Github(
            client_id=settings.GITHUB_CLIENT_ID,
            client_secret=settings.GITHUB_CLIENT_SECRET
        )
        self.db = db
    
    def get_repository(self, full_name: str) -> Dict[str, Any]:
        """
        Get repository data from GitHub
        
        Args:
            full_name: Full name of repository (owner/repo)
            
        Returns:
            Repository data as dictionary
        """
        try:
            repo = self.github.get_repo(full_name)
            return {
                "name": repo.name,
                "full_name": repo.full_name,
                "description": repo.description,
                "url": repo.url,
                "html_url": repo.html_url,
                "clone_url": repo.clone_url,
                "default_branch": repo.default_branch,
                "stars": repo.stargazers_count,
                "forks": repo.forks_count,
                "open_issues": repo.open_issues_count
            }
        except GithubException as e:
            logger.error(f"Error fetching repository {full_name}: {str(e)}")
            raise
    
    def sync_repository(self, full_name: str) -> Repository:
        """
        Sync repository data from GitHub to database
        
        Args:
            full_name: Full name of repository (owner/repo)
            
        Returns:
            Repository model instance
        """
        # Get repository data from GitHub
        repo_data = self.get_repository(full_name)
        
        # Check if repository exists in database
        db_repo = self.db.query(Repository).filter(Repository.full_name == full_name).first()
        
        if db_repo:
            # Update existing repository
            for key, value in repo_data.items():
                setattr(db_repo, key, value)
            db_repo.updated_at = datetime.utcnow()
        else:
            # Create new repository
            db_repo = Repository(**repo_data)
            self.db.add(db_repo)
        
        db_repo.last_synced_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(db_repo)
        
        # Sync commits
        self._sync_commits(db_repo)
        
        # Sync special files
        self._sync_special_files(db_repo)
        
        return db_repo
    
    def _sync_commits(self, db_repo: Repository, limit: int = 100) -> None:
        """
        Sync recent commits for a repository
        
        Args:
            db_repo: Repository model instance
            limit: Maximum number of commits to sync
        """
        try:
            repo = self.github.get_repo(db_repo.full_name)
            commits = repo.get_commits()[:limit]
            
            for commit in commits:
                # Check if commit exists in database
                db_commit = self.db.query(Commit).filter(
                    Commit.repository_id == db_repo.id,
                    Commit.sha == commit.sha
                ).first()
                
                if not db_commit:
                    # Create new commit
                    commit_data = {
                        "repository_id": db_repo.id,
                        "sha": commit.sha,
                        "message": commit.commit.message,
                        "author": commit.commit.author.name,
                        "author_email": commit.commit.author.email,
                        "committed_date": commit.commit.author.date
                    }
                    db_commit = Commit(**commit_data)
                    self.db.add(db_commit)
            
            self.db.commit()
        except GithubException as e:
            logger.error(f"Error syncing commits for {db_repo.full_name}: {str(e)}")
    
    def _sync_special_files(self, db_repo: Repository) -> None:
        """
        Sync special configuration files (.dockerwrapper, .claude, .cursor)
        
        Args:
            db_repo: Repository model instance
        """
        try:
            repo = self.github.get_repo(db_repo.full_name)
            special_file_paths = [
                ".dockerwrapper",
                ".claude",
                ".cursor"
            ]
            
            for file_path in special_file_paths:
                try:
                    # Try to get file content
                    file_type = file_path.lstrip(".")
                    
                    # Check if file exists in database
                    db_file = self.db.query(SpecialFile).filter(
                        SpecialFile.repository_id == db_repo.id,
                        SpecialFile.file_type == file_type
                    ).first()
                    
                    try:
                        # Try to get file content
                        content = repo.get_contents(file_path)
                        file_content = content.decoded_content.decode("utf-8")
                        
                        if db_file:
                            # Update existing file
                            db_file.content = file_content
                            db_file.updated_at = datetime.utcnow()
                        else:
                            # Create new file
                            db_file = SpecialFile(
                                repository_id=db_repo.id,
                                file_type=file_type,
                                file_path=file_path,
                                content=file_content
                            )
                            self.db.add(db_file)
                    except GithubException as e:
                        # File not found, check if it's a directory
                        if e.status == 404:
                            # Try to get as directory
                            try:
                                contents = repo.get_contents(file_path)
                                # It's a directory, mark it as such
                                if db_file:
                                    db_file.content = "Directory structure"
                                    db_file.updated_at = datetime.utcnow()
                                else:
                                    db_file = SpecialFile(
                                        repository_id=db_repo.id,
                                        file_type=file_type,
                                        file_path=file_path,
                                        content="Directory structure"
                                    )
                                    self.db.add(db_file)
                            except GithubException:
                                # Neither file nor directory exists
                                if db_file:
                                    # Remove file if it no longer exists
                                    self.db.delete(db_file)
                        else:
                            logger.error(f"Error fetching {file_path} for {db_repo.full_name}: {str(e)}")
                except Exception as e:
                    logger.error(f"Error processing {file_path} for {db_repo.full_name}: {str(e)}")
            
            self.db.commit()
        except GithubException as e:
            logger.error(f"Error syncing special files for {db_repo.full_name}: {str(e)}")


def sync_all_repositories(db: Session) -> None:
    """
    Sync all active repositories in the database
    
    Args:
        db: Database session
    """
    repositories = db.query(Repository).filter(Repository.is_active == True).all()
    
    for repo in repositories:
        try:
            github_service = GitHubService(db)
            github_service.sync_repository(repo.full_name)
            logger.info(f"Successfully synced repository: {repo.full_name}")
        except Exception as e:
            logger.error(f"Error syncing repository {repo.full_name}: {str(e)}") 