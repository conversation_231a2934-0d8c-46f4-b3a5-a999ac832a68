"""
Base model for all database models
"""
from sqlalchemy import Column, DateTime, Boolean
from sqlalchemy.ext.declarative import declared_attr
from datetime import datetime

from app.core.database import Base


class BaseModel:
    """
    Base model class that includes soft-delete functionality and common timestamp fields
    """
    
    # Common timestamp fields
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    deleted_at = Column(DateTime, nullable=True)
    
    # Soft-delete flag
    is_deleted = Column(Boolean, default=False, nullable=False)
    
    @declared_attr
    def __tablename__(cls):
        """
        Generate __tablename__ automatically from class name
        """
        return cls.__name__.lower() + 's'
    
    @classmethod
    def get_by_id(cls, db_session, id):
        """
        Get a record by ID, excluding soft-deleted records
        """
        return db_session.query(cls).filter(cls.id == id, cls.is_deleted == False).first()
    
    @classmethod
    def get_all(cls, db_session, skip=0, limit=100):
        """
        Get all records, excluding soft-deleted records
        """
        return db_session.query(cls).filter(cls.is_deleted == False).offset(skip).limit(limit).all()
    
    def soft_delete(self, db_session):
        """
        Soft delete a record by setting is_deleted flag and deleted_at timestamp
        """
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        db_session.add(self)
        db_session.commit()
        return self
    
    def hard_delete(self, db_session):
        """
        Hard delete a record from the database
        """
        db_session.delete(self)
        db_session.commit()
        return True 