from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.orm import relationship
from datetime import datetime

from app.core.database import Base
from app.models.user import user_repository
from app.models.base import BaseModel


class Repository(Base, BaseModel):
    """
    Model for GitHub repositories
    """
    __tablename__ = "repositories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    full_name = Column(String, unique=True, index=True)
    description = Column(Text, nullable=True)
    url = Column(String)
    html_url = Column(String)
    clone_url = Column(String)
    default_branch = Column(String)
    stars = Column(Integer, default=0)
    forks = Column(Integer, default=0)
    open_issues = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_synced_at = Column(DateTime, nullable=True)

    # Relationships
    special_files = relationship("SpecialFile", back_populates="repository", cascade="all, delete-orphan")
    commits = relationship("Commit", back_populates="repository", cascade="all, delete-orphan")
    milestones = relationship("Milestone", back_populates="repository", cascade="all, delete-orphan")
    users = relationship("User", secondary=user_repository, back_populates="repositories")
    notifications = relationship("Notification", back_populates="repository", cascade="all, delete-orphan")


class SpecialFile(Base, BaseModel):
    """
    Model for special configuration files (.dockerwrapper, .claude, .cursor)
    """
    __tablename__ = "special_files"

    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(Integer, ForeignKey("repositories.id", ondelete="CASCADE"))
    file_type = Column(String, index=True)  # dockerwrapper, claude, cursor
    file_path = Column(String)
    content = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    repository = relationship("Repository", back_populates="special_files")


class Commit(Base, BaseModel):
    """
    Model for repository commits
    """
    __tablename__ = "commits"

    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(Integer, ForeignKey("repositories.id", ondelete="CASCADE"))
    sha = Column(String, index=True)
    message = Column(Text)
    author = Column(String)
    author_email = Column(String, nullable=True)
    committed_date = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    repository = relationship("Repository", back_populates="commits")


class Milestone(Base, BaseModel):
    """
    Model for repository milestones and key features
    """
    __tablename__ = "milestones"

    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(Integer, ForeignKey("repositories.id", ondelete="CASCADE"))
    title = Column(String)
    description = Column(Text, nullable=True)
    due_date = Column(DateTime, nullable=True)
    is_completed = Column(Boolean, default=False)
    completed_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    repository = relationship("Repository", back_populates="milestones") 