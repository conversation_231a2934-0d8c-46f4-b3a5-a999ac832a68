"""Add soft delete functionality to all models

Revision ID: 20230601_add_soft_delete
Revises: 
Create Date: 2023-06-01 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20230601_add_soft_delete'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Add soft delete columns to repositories table
    op.add_column('repositories', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default='false'))
    op.add_column('repositories', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    
    # Add soft delete columns to special_files table
    op.add_column('special_files', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('special_files', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    
    # Add soft delete columns to commits table
    op.add_column('commits', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default='false'))
    op.add_column('commits', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    
    # Add soft delete columns to milestones table
    op.add_column('milestones', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('milestones', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    
    # Add soft delete columns to users table
    op.add_column('users', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('users', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    
    # Add soft delete columns to notifications table
    op.add_column('notifications', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('notifications', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    
    # Add soft delete columns to user_preferences table
    op.add_column('user_preferences', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('user_preferences', sa.Column('deleted_at', sa.DateTime(), nullable=True))


def downgrade():
    # Remove soft delete columns from repositories table
    op.drop_column('repositories', 'deleted_at')
    op.drop_column('repositories', 'is_deleted')
    
    # Remove soft delete columns from special_files table
    op.drop_column('special_files', 'deleted_at')
    op.drop_column('special_files', 'is_deleted')
    
    # Remove soft delete columns from commits table
    op.drop_column('commits', 'deleted_at')
    op.drop_column('commits', 'is_deleted')
    
    # Remove soft delete columns from milestones table
    op.drop_column('milestones', 'deleted_at')
    op.drop_column('milestones', 'is_deleted')
    
    # Remove soft delete columns from users table
    op.drop_column('users', 'deleted_at')
    op.drop_column('users', 'is_deleted')
    
    # Remove soft delete columns from notifications table
    op.drop_column('notifications', 'deleted_at')
    op.drop_column('notifications', 'is_deleted')
    
    # Remove soft delete columns from user_preferences table
    op.drop_column('user_preferences', 'deleted_at')
    op.drop_column('user_preferences', 'is_deleted') 