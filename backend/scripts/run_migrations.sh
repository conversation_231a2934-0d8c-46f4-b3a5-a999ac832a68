#!/bin/bash

# Script to run Alembic migrations

# Navigate to the backend directory
cd "$(dirname "$0")/.."

# Check if alembic is installed
if ! command -v alembic &> /dev/null; then
    echo "Error: alembic is not installed. Please install it first."
    echo "pip install alembic"
    exit 1
fi

# Function to display usage
function display_usage {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  upgrade [revision]    Upgrade to the latest revision or specified revision"
    echo "  downgrade [revision]  Downgrade to the previous revision or specified revision"
    echo "  revision [message]    Create a new revision with optional message"
    echo "  history               Show migration history"
    echo "  current               Show current revision"
    echo "  stamp [revision]      Mark a revision as applied without running it"
    echo "  help                  Display this help message"
    echo ""
    echo "Examples:"
    echo "  $0 upgrade            # Upgrade to the latest revision"
    echo "  $0 upgrade +1         # Upgrade by one revision"
    echo "  $0 upgrade abc123     # Upgrade to specific revision"
    echo "  $0 downgrade -1       # Downgrade by one revision"
    echo "  $0 revision \"Add users table\"  # Create a new revision"
}

# Check if a command is provided
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

# Parse command
command=$1
shift

case $command in
    upgrade)
        if [ $# -eq 0 ]; then
            echo "Running migration upgrade to head..."
            alembic upgrade head
        else
            echo "Running migration upgrade to $1..."
            alembic upgrade "$1"
        fi
        ;;
    downgrade)
        if [ $# -eq 0 ]; then
            echo "Running migration downgrade by 1..."
            alembic downgrade -1
        else
            echo "Running migration downgrade to $1..."
            alembic downgrade "$1"
        fi
        ;;
    revision)
        if [ $# -eq 0 ]; then
            echo "Creating new revision..."
            alembic revision --autogenerate
        else
            echo "Creating new revision with message: $1..."
            alembic revision --autogenerate -m "$1"
        fi
        ;;
    history)
        echo "Showing migration history..."
        alembic history
        ;;
    current)
        echo "Showing current revision..."
        alembic current
        ;;
    stamp)
        if [ $# -eq 0 ]; then
            echo "Error: No revision specified for stamp command."
            display_usage
            exit 1
        else
            echo "Stamping revision $1..."
            alembic stamp "$1"
        fi
        ;;
    help)
        display_usage
        ;;
    *)
        echo "Error: Unknown command '$command'"
        display_usage
        exit 1
        ;;
esac

echo "Done." 