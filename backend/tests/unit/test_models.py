"""
Unit tests for database models
"""
import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.models.user import User, Notification, UserPreference
from app.models.repository import Repository, SpecialFile, Commit, Milestone


@pytest.mark.unit
class TestUserModel:
    """Test User model functionality."""

    def test_create_user(self, db_session: Session):
        """Test creating a new user."""
        user = User(
            username="testuser",
            email="<EMAIL>",
            github_id="123456",
            github_token="test_token"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)

        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.github_id == "123456"
        assert user.is_active is True
        assert user.is_superuser is False
        assert user.created_at is not None

    def test_user_relationships(self, db_session: Session, test_user: User, test_repository: Repository):
        """Test user relationships with repositories."""
        # Add repository to user
        test_user.repositories.append(test_repository)
        db_session.commit()

        # Verify relationship
        assert len(test_user.repositories) == 1
        assert test_user.repositories[0].id == test_repository.id
        assert test_repository in test_user.repositories

    def test_user_preferences(self, db_session: Session, test_user: User):
        """Test user preferences relationship."""
        preference = UserPreference(
            user_id=test_user.id,
            theme="dark",
            email_notifications=False,
            dashboard_layout='{"layout": "grid"}'
        )
        db_session.add(preference)
        db_session.commit()
        db_session.refresh(test_user)

        assert test_user.preferences is not None
        assert test_user.preferences.theme == "dark"
        assert test_user.preferences.email_notifications is False


@pytest.mark.unit
class TestRepositoryModel:
    """Test Repository model functionality."""

    def test_create_repository(self, db_session: Session):
        """Test creating a new repository."""
        repo = Repository(
            name="test-repo",
            full_name="user/test-repo",
            description="Test repository",
            url="https://api.github.com/repos/user/test-repo",
            html_url="https://github.com/user/test-repo",
            clone_url="https://github.com/user/test-repo.git",
            default_branch="main",
            stars=100,
            forks=20,
            open_issues=5
        )
        db_session.add(repo)
        db_session.commit()
        db_session.refresh(repo)

        assert repo.id is not None
        assert repo.name == "test-repo"
        assert repo.full_name == "user/test-repo"
        assert repo.stars == 100
        assert repo.is_active is True
        assert repo.created_at is not None

    def test_repository_soft_delete(self, db_session: Session, test_repository: Repository):
        """Test soft delete functionality."""
        # Soft delete the repository
        test_repository.soft_delete()
        db_session.commit()

        assert test_repository.is_deleted is True
        assert test_repository.deleted_at is not None

    def test_repository_relationships(self, db_session: Session, test_repository: Repository):
        """Test repository relationships."""
        # Add special file
        special_file = SpecialFile(
            repository_id=test_repository.id,
            file_type="dockerwrapper",
            file_path=".dockerwrapper",
            content="test content"
        )
        db_session.add(special_file)

        # Add commit
        commit = Commit(
            repository_id=test_repository.id,
            sha="abc123",
            message="Test commit",
            author="Test Author",
            committed_date=datetime.utcnow()
        )
        db_session.add(commit)

        # Add milestone
        milestone = Milestone(
            repository_id=test_repository.id,
            title="Test Milestone",
            description="Test milestone description"
        )
        db_session.add(milestone)

        db_session.commit()
        db_session.refresh(test_repository)

        assert len(test_repository.special_files) == 1
        assert len(test_repository.commits) == 1
        assert len(test_repository.milestones) == 1


@pytest.mark.unit
class TestSpecialFileModel:
    """Test SpecialFile model functionality."""

    def test_create_special_file(self, db_session: Session, test_repository: Repository):
        """Test creating a special file."""
        special_file = SpecialFile(
            repository_id=test_repository.id,
            file_type="claude",
            file_path=".claude",
            content="claude configuration"
        )
        db_session.add(special_file)
        db_session.commit()
        db_session.refresh(special_file)

        assert special_file.id is not None
        assert special_file.file_type == "claude"
        assert special_file.file_path == ".claude"
        assert special_file.repository_id == test_repository.id
        assert special_file.created_at is not None


@pytest.mark.unit
class TestCommitModel:
    """Test Commit model functionality."""

    def test_create_commit(self, db_session: Session, test_repository: Repository):
        """Test creating a commit."""
        commit_date = datetime.utcnow()
        commit = Commit(
            repository_id=test_repository.id,
            sha="def456abc789",
            message="Add new feature",
            author="Developer",
            author_email="<EMAIL>",
            committed_date=commit_date
        )
        db_session.add(commit)
        db_session.commit()
        db_session.refresh(commit)

        assert commit.id is not None
        assert commit.sha == "def456abc789"
        assert commit.message == "Add new feature"
        assert commit.author == "Developer"
        assert commit.committed_date == commit_date


@pytest.mark.unit
class TestMilestoneModel:
    """Test Milestone model functionality."""

    def test_create_milestone(self, db_session: Session, test_repository: Repository):
        """Test creating a milestone."""
        due_date = datetime.utcnow() + timedelta(days=30)
        milestone = Milestone(
            repository_id=test_repository.id,
            title="Version 1.0",
            description="First major release",
            due_date=due_date,
            is_completed=False
        )
        db_session.add(milestone)
        db_session.commit()
        db_session.refresh(milestone)

        assert milestone.id is not None
        assert milestone.title == "Version 1.0"
        assert milestone.due_date == due_date
        assert milestone.is_completed is False
        assert milestone.completed_date is None

    def test_complete_milestone(self, db_session: Session, test_milestone: Milestone):
        """Test completing a milestone."""
        completion_date = datetime.utcnow()
        test_milestone.is_completed = True
        test_milestone.completed_date = completion_date
        db_session.commit()

        assert test_milestone.is_completed is True
        assert test_milestone.completed_date == completion_date
