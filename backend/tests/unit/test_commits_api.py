"""
Unit tests for commits API endpoints
"""
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from unittest.mock import patch

from app.models.repository import Repository, Commit


@pytest.mark.unit
class TestCommitsAPI:
    """Test commits API endpoints."""

    def test_get_commits_success(self, client: TestClient, test_commit: Commit):
        """Test getting all commits successfully."""
        response = client.get("/api/v1/commits")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["sha"] == test_commit.sha
        assert data[0]["message"] == test_commit.message
        assert data[0]["author"] == test_commit.author

    def test_get_commits_with_repository_filter(self, client: TestClient, test_commit: Commit, db_session: Session):
        """Test getting commits filtered by repository."""
        # Create another repository and commit
        other_repo = Repository(
            name="other-repo",
            full_name="user/other-repo",
            url="https://api.github.com/repos/user/other-repo",
            html_url="https://github.com/user/other-repo",
            clone_url="https://github.com/user/other-repo.git",
            default_branch="main"
        )
        db_session.add(other_repo)
        db_session.commit()
        db_session.refresh(other_repo)

        other_commit = Commit(
            repository_id=other_repo.id,
            sha="xyz789",
            message="Other commit",
            author="Other Author",
            committed_date=datetime.utcnow()
        )
        db_session.add(other_commit)
        db_session.commit()

        # Test filtering by repository
        response = client.get(f"/api/v1/commits?repository_id={test_commit.repository_id}")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["sha"] == test_commit.sha

    def test_get_commits_with_author_filter(self, client: TestClient, test_commit: Commit, db_session: Session):
        """Test getting commits filtered by author."""
        # Create another commit with different author
        other_commit = Commit(
            repository_id=test_commit.repository_id,
            sha="xyz789",
            message="Other commit",
            author="Different Author",
            committed_date=datetime.utcnow()
        )
        db_session.add(other_commit)
        db_session.commit()

        # Test filtering by author
        response = client.get(f"/api/v1/commits?author={test_commit.author}")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["author"] == test_commit.author

    def test_get_commits_with_date_filters(self, client: TestClient, db_session: Session, test_repository: Repository):
        """Test getting commits with date range filters."""
        now = datetime.utcnow()
        yesterday = now - timedelta(days=1)
        tomorrow = now + timedelta(days=1)

        # Create commits with different dates
        old_commit = Commit(
            repository_id=test_repository.id,
            sha="old123",
            message="Old commit",
            author="Author",
            committed_date=yesterday
        )
        new_commit = Commit(
            repository_id=test_repository.id,
            sha="new123",
            message="New commit",
            author="Author",
            committed_date=now
        )
        db_session.add_all([old_commit, new_commit])
        db_session.commit()

        # Test since filter
        response = client.get(f"/api/v1/commits?since={now.isoformat()}")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["sha"] == "new123"

        # Test until filter
        response = client.get(f"/api/v1/commits?until={yesterday.isoformat()}")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["sha"] == "old123"

    def test_get_commit_by_id_success(self, client: TestClient, test_commit: Commit):
        """Test getting a specific commit by ID."""
        response = client.get(f"/api/v1/commits/{test_commit.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_commit.id
        assert data["sha"] == test_commit.sha
        assert data["message"] == test_commit.message

    def test_get_commit_by_id_not_found(self, client: TestClient):
        """Test getting a non-existent commit."""
        response = client.get("/api/v1/commits/999")
        
        assert response.status_code == 404
        assert "Commit not found" in response.json()["detail"]

    def test_create_commit_success(self, client: TestClient, test_repository: Repository):
        """Test creating a new commit."""
        commit_data = {
            "repository_id": test_repository.id,
            "sha": "new123abc",
            "message": "New commit message",
            "author": "New Author",
            "author_email": "<EMAIL>",
            "committed_date": datetime.utcnow().isoformat()
        }

        response = client.post("/api/v1/commits", json=commit_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["sha"] == commit_data["sha"]
        assert data["message"] == commit_data["message"]
        assert data["author"] == commit_data["author"]

    def test_create_commit_repository_not_found(self, client: TestClient):
        """Test creating a commit for non-existent repository."""
        commit_data = {
            "repository_id": 999,
            "sha": "new123abc",
            "message": "New commit message",
            "author": "New Author",
            "committed_date": datetime.utcnow().isoformat()
        }

        response = client.post("/api/v1/commits", json=commit_data)
        
        assert response.status_code == 404
        assert "Repository not found" in response.json()["detail"]

    def test_create_commit_duplicate_sha(self, client: TestClient, test_commit: Commit):
        """Test creating a commit with duplicate SHA."""
        commit_data = {
            "repository_id": test_commit.repository_id,
            "sha": test_commit.sha,  # Same SHA
            "message": "Duplicate commit",
            "author": "Author",
            "committed_date": datetime.utcnow().isoformat()
        }

        response = client.post("/api/v1/commits", json=commit_data)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    def test_update_commit_success(self, client: TestClient, test_commit: Commit):
        """Test updating a commit."""
        update_data = {
            "message": "Updated commit message",
            "author": "Updated Author"
        }

        response = client.put(f"/api/v1/commits/{test_commit.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Updated commit message"
        assert data["author"] == "Updated Author"

    def test_update_commit_not_found(self, client: TestClient):
        """Test updating a non-existent commit."""
        update_data = {
            "message": "Updated message"
        }

        response = client.put("/api/v1/commits/999", json=update_data)
        
        assert response.status_code == 404
        assert "Commit not found" in response.json()["detail"]

    def test_delete_commit_soft(self, client: TestClient, test_commit: Commit, db_session: Session):
        """Test soft deleting a commit."""
        response = client.delete(f"/api/v1/commits/{test_commit.id}")
        
        assert response.status_code == 200
        
        # Verify soft delete
        db_session.refresh(test_commit)
        assert test_commit.is_deleted is True
        assert test_commit.deleted_at is not None

    def test_delete_commit_permanent(self, client: TestClient, test_commit: Commit, db_session: Session):
        """Test permanently deleting a commit."""
        commit_id = test_commit.id
        response = client.delete(f"/api/v1/commits/{commit_id}?permanent=true")
        
        assert response.status_code == 200
        
        # Verify permanent delete
        deleted_commit = db_session.query(Commit).filter(Commit.id == commit_id).first()
        assert deleted_commit is None

    def test_restore_commit_success(self, client: TestClient, test_commit: Commit, db_session: Session):
        """Test restoring a soft-deleted commit."""
        # First soft delete the commit
        test_commit.soft_delete()
        db_session.commit()

        response = client.post(f"/api/v1/commits/{test_commit.id}/restore")
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_deleted"] is False
        assert data["deleted_at"] is None

    @patch('app.services.github_service.GitHubService._sync_commits')
    def test_sync_commits_success(self, mock_sync, client: TestClient, test_repository: Repository):
        """Test syncing commits from GitHub."""
        response = client.post(f"/api/v1/commits/sync/{test_repository.id}")
        
        assert response.status_code == 200
        mock_sync.assert_called_once()

    @patch('app.services.github_service.GitHubService._sync_commits')
    def test_sync_commits_repository_not_found(self, mock_sync, client: TestClient):
        """Test syncing commits for non-existent repository."""
        response = client.post("/api/v1/commits/sync/999")
        
        assert response.status_code == 404
        assert "Repository not found" in response.json()["detail"]
        mock_sync.assert_not_called()

    @patch('app.services.github_service.GitHubService._sync_commits')
    def test_sync_commits_error(self, mock_sync, client: TestClient, test_repository: Repository):
        """Test syncing commits with GitHub API error."""
        mock_sync.side_effect = Exception("GitHub API error")

        response = client.post(f"/api/v1/commits/sync/{test_repository.id}")
        
        assert response.status_code == 400
        assert "Error syncing commits" in response.json()["detail"]
