"""
Unit tests for repository API endpoints
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import patch, MagicMock

from app.models.repository import Repository


@pytest.mark.unit
class TestRepositoriesAPI:
    """Test repository API endpoints."""

    def test_get_repositories_success(self, client: TestClient, test_repository: Repository):
        """Test getting all repositories successfully."""
        response = client.get("/api/v1/repositories")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == test_repository.name
        assert data[0]["full_name"] == test_repository.full_name

    def test_get_repositories_with_filters(self, client: TestClient, test_repository: Repository):
        """Test getting repositories with filters."""
        # Test active filter
        response = client.get("/api/v1/repositories?is_active=true")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1

        # Test inactive filter
        response = client.get("/api/v1/repositories?is_active=false")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 0

    def test_get_repositories_pagination(self, client: TestClient, db_session: Session):
        """Test repository pagination."""
        # Create multiple repositories
        for i in range(5):
            repo = Repository(
                name=f"repo-{i}",
                full_name=f"user/repo-{i}",
                url=f"https://api.github.com/repos/user/repo-{i}",
                html_url=f"https://github.com/user/repo-{i}",
                clone_url=f"https://github.com/user/repo-{i}.git",
                default_branch="main"
            )
            db_session.add(repo)
        db_session.commit()

        # Test pagination
        response = client.get("/api/v1/repositories?skip=0&limit=3")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3

        response = client.get("/api/v1/repositories?skip=3&limit=3")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3

    def test_get_repository_by_id_success(self, client: TestClient, test_repository: Repository):
        """Test getting a specific repository by ID."""
        response = client.get(f"/api/v1/repositories/{test_repository.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_repository.id
        assert data["name"] == test_repository.name
        assert data["full_name"] == test_repository.full_name

    def test_get_repository_by_id_not_found(self, client: TestClient):
        """Test getting a non-existent repository."""
        response = client.get("/api/v1/repositories/999")
        
        assert response.status_code == 404
        assert "Repository not found" in response.json()["detail"]

    def test_get_repository_by_id_deleted(self, client: TestClient, test_repository: Repository, db_session: Session):
        """Test getting a soft-deleted repository."""
        # Soft delete the repository
        test_repository.soft_delete()
        db_session.commit()

        # Should not be found by default
        response = client.get(f"/api/v1/repositories/{test_repository.id}")
        assert response.status_code == 404

        # Should be found when including deleted
        response = client.get(f"/api/v1/repositories/{test_repository.id}?include_deleted=true")
        assert response.status_code == 200

    @patch('app.services.github_service.GitHubService.sync_repository')
    def test_create_repository_success(self, mock_sync, client: TestClient, db_session: Session):
        """Test creating a new repository."""
        # Mock the GitHub service
        mock_repo = Repository(
            name="new-repo",
            full_name="user/new-repo",
            description="New repository",
            url="https://api.github.com/repos/user/new-repo",
            html_url="https://github.com/user/new-repo",
            clone_url="https://github.com/user/new-repo.git",
            default_branch="main"
        )
        mock_repo.id = 1
        mock_sync.return_value = mock_repo

        repository_data = {
            "full_name": "user/new-repo"
        }

        response = client.post("/api/v1/repositories", json=repository_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["full_name"] == "user/new-repo"
        mock_sync.assert_called_once_with("user/new-repo")

    def test_create_repository_duplicate(self, client: TestClient, test_repository: Repository):
        """Test creating a duplicate repository."""
        repository_data = {
            "full_name": test_repository.full_name
        }

        response = client.post("/api/v1/repositories", json=repository_data)
        
        assert response.status_code == 400
        assert "already being tracked" in response.json()["detail"]

    @patch('app.services.github_service.GitHubService.sync_repository')
    def test_create_repository_github_error(self, mock_sync, client: TestClient):
        """Test creating repository with GitHub API error."""
        mock_sync.side_effect = Exception("GitHub API error")

        repository_data = {
            "full_name": "user/nonexistent-repo"
        }

        response = client.post("/api/v1/repositories", json=repository_data)
        
        assert response.status_code == 400
        assert "Error fetching repository from GitHub" in response.json()["detail"]

    def test_update_repository_success(self, client: TestClient, test_repository: Repository):
        """Test updating a repository."""
        update_data = {
            "description": "Updated description",
            "is_active": False
        }

        response = client.put(f"/api/v1/repositories/{test_repository.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["description"] == "Updated description"
        assert data["is_active"] is False

    def test_update_repository_not_found(self, client: TestClient):
        """Test updating a non-existent repository."""
        update_data = {
            "description": "Updated description"
        }

        response = client.put("/api/v1/repositories/999", json=update_data)
        
        assert response.status_code == 404
        assert "Repository not found" in response.json()["detail"]

    def test_delete_repository_soft(self, client: TestClient, test_repository: Repository, db_session: Session):
        """Test soft deleting a repository."""
        response = client.delete(f"/api/v1/repositories/{test_repository.id}")
        
        assert response.status_code == 200
        
        # Verify soft delete
        db_session.refresh(test_repository)
        assert test_repository.is_deleted is True
        assert test_repository.deleted_at is not None

    def test_delete_repository_permanent(self, client: TestClient, test_repository: Repository, db_session: Session):
        """Test permanently deleting a repository."""
        repo_id = test_repository.id
        response = client.delete(f"/api/v1/repositories/{repo_id}?permanent=true")
        
        assert response.status_code == 200
        
        # Verify permanent delete
        deleted_repo = db_session.query(Repository).filter(Repository.id == repo_id).first()
        assert deleted_repo is None

    def test_delete_repository_not_found(self, client: TestClient):
        """Test deleting a non-existent repository."""
        response = client.delete("/api/v1/repositories/999")
        
        assert response.status_code == 404
        assert "Repository not found" in response.json()["detail"]

    def test_restore_repository_success(self, client: TestClient, test_repository: Repository, db_session: Session):
        """Test restoring a soft-deleted repository."""
        # First soft delete the repository
        test_repository.soft_delete()
        db_session.commit()

        response = client.post(f"/api/v1/repositories/{test_repository.id}/restore")
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_deleted"] is False
        assert data["deleted_at"] is None

    def test_restore_repository_not_deleted(self, client: TestClient, test_repository: Repository):
        """Test restoring a repository that is not deleted."""
        response = client.post(f"/api/v1/repositories/{test_repository.id}/restore")
        
        assert response.status_code == 400
        assert "not deleted" in response.json()["detail"]

    @patch('app.services.github_service.GitHubService.sync_repository')
    def test_sync_repository_success(self, mock_sync, client: TestClient, test_repository: Repository):
        """Test syncing repository with GitHub."""
        mock_sync.return_value = test_repository

        response = client.post(f"/api/v1/repositories/{test_repository.id}/sync")
        
        assert response.status_code == 200
        mock_sync.assert_called_once_with(test_repository.full_name)

    @patch('app.services.github_service.GitHubService.sync_repository')
    def test_sync_repository_error(self, mock_sync, client: TestClient, test_repository: Repository):
        """Test syncing repository with GitHub API error."""
        mock_sync.side_effect = Exception("GitHub API error")

        response = client.post(f"/api/v1/repositories/{test_repository.id}/sync")
        
        assert response.status_code == 400
        assert "Error syncing repository" in response.json()["detail"]
