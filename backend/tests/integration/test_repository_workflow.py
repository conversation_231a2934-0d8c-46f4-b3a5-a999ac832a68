"""
Integration tests for repository workflow
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import patch, MagicMock

from app.models.repository import Repository, SpecialFile, Commit, Milestone


@pytest.mark.integration
class TestRepositoryWorkflow:
    """Test complete repository management workflow."""

    @patch('app.services.github_service.GitHubService.sync_repository')
    @patch('app.services.github_service.GitHubService._sync_special_files')
    @patch('app.services.github_service.GitHubService._sync_commits')
    def test_complete_repository_lifecycle(self, mock_sync_commits, mock_sync_files, mock_sync_repo, 
                                         client: TestClient, db_session: Session):
        """Test complete repository lifecycle from creation to deletion."""
        
        # Mock GitHub service responses
        mock_repo = Repository(
            name="test-repo",
            full_name="user/test-repo",
            description="Test repository",
            url="https://api.github.com/repos/user/test-repo",
            html_url="https://github.com/user/test-repo",
            clone_url="https://github.com/user/test-repo.git",
            default_branch="main",
            stars=10,
            forks=2
        )
        mock_repo.id = 1
        mock_sync_repo.return_value = mock_repo

        # 1. Create repository
        repository_data = {"full_name": "user/test-repo"}
        response = client.post("/api/v1/repositories", json=repository_data)
        
        assert response.status_code == 201
        repo_data = response.json()
        repo_id = repo_data["id"]
        
        # 2. Verify repository was created
        response = client.get(f"/api/v1/repositories/{repo_id}")
        assert response.status_code == 200
        assert response.json()["full_name"] == "user/test-repo"
        
        # 3. Add a milestone
        milestone_data = {
            "repository_id": repo_id,
            "title": "Version 1.0",
            "description": "First release"
        }
        response = client.post("/api/v1/milestones", json=milestone_data)
        assert response.status_code == 201
        milestone_id = response.json()["id"]
        
        # 4. Add a commit
        commit_data = {
            "repository_id": repo_id,
            "sha": "abc123def456",
            "message": "Initial commit",
            "author": "Test Author",
            "committed_date": "2023-01-01T00:00:00Z"
        }
        response = client.post("/api/v1/commits", json=commit_data)
        assert response.status_code == 201
        commit_id = response.json()["id"]
        
        # 5. Add a special file
        special_file_data = {
            "repository_id": repo_id,
            "file_type": "dockerwrapper",
            "file_path": ".dockerwrapper",
            "content": "test content"
        }
        response = client.post("/api/v1/special-files", json=special_file_data)
        assert response.status_code == 201
        file_id = response.json()["id"]
        
        # 6. Verify repository details include all related data
        response = client.get(f"/api/v1/repositories/{repo_id}")
        assert response.status_code == 200
        repo_detail = response.json()
        
        # Check relationships exist in database
        repository = db_session.query(Repository).filter(Repository.id == repo_id).first()
        assert len(repository.milestones) == 1
        assert len(repository.commits) == 1
        assert len(repository.special_files) == 1
        
        # 7. Update repository
        update_data = {"description": "Updated description"}
        response = client.put(f"/api/v1/repositories/{repo_id}", json=update_data)
        assert response.status_code == 200
        assert response.json()["description"] == "Updated description"
        
        # 8. Complete milestone
        response = client.post(f"/api/v1/milestones/{milestone_id}/complete")
        assert response.status_code == 200
        assert response.json()["is_completed"] is True
        
        # 9. Soft delete repository
        response = client.delete(f"/api/v1/repositories/{repo_id}")
        assert response.status_code == 200
        
        # 10. Verify repository is soft deleted
        response = client.get(f"/api/v1/repositories/{repo_id}")
        assert response.status_code == 404
        
        # 11. Verify repository can be found with include_deleted
        response = client.get(f"/api/v1/repositories/{repo_id}?include_deleted=true")
        assert response.status_code == 200
        assert response.json()["is_deleted"] is True
        
        # 12. Restore repository
        response = client.post(f"/api/v1/repositories/{repo_id}/restore")
        assert response.status_code == 200
        assert response.json()["is_deleted"] is False
        
        # 13. Permanently delete repository
        response = client.delete(f"/api/v1/repositories/{repo_id}?permanent=true")
        assert response.status_code == 200
        
        # 14. Verify repository is permanently deleted
        repository = db_session.query(Repository).filter(Repository.id == repo_id).first()
        assert repository is None

    @patch('app.services.github_service.GitHubService._sync_special_files')
    def test_special_files_sync_workflow(self, mock_sync_files, client: TestClient, 
                                       test_repository: Repository, db_session: Session):
        """Test special files synchronization workflow."""
        
        # Mock GitHub service to return special files
        def mock_sync_side_effect(repository):
            # Create special files in database
            files_data = [
                {"file_type": "dockerwrapper", "file_path": ".dockerwrapper", "content": "wrapper content"},
                {"file_type": "claude", "file_path": ".claude", "content": "claude config"},
                {"file_type": "cursor", "file_path": ".cursor", "content": "cursor config"}
            ]
            
            for file_data in files_data:
                special_file = SpecialFile(
                    repository_id=repository.id,
                    **file_data
                )
                db_session.add(special_file)
            db_session.commit()
        
        mock_sync_files.side_effect = mock_sync_side_effect
        
        # 1. Sync special files
        response = client.post(f"/api/v1/special-files/sync/{test_repository.id}")
        assert response.status_code == 200
        synced_files = response.json()
        assert len(synced_files) == 3
        
        # 2. Verify files were created
        response = client.get(f"/api/v1/special-files?repository_id={test_repository.id}")
        assert response.status_code == 200
        files = response.json()
        assert len(files) == 3
        
        file_types = [f["file_type"] for f in files]
        assert "dockerwrapper" in file_types
        assert "claude" in file_types
        assert "cursor" in file_types
        
        # 3. Update a special file
        file_id = files[0]["id"]
        update_data = {"content": "updated content"}
        response = client.put(f"/api/v1/special-files/{file_id}", json=update_data)
        assert response.status_code == 200
        assert response.json()["content"] == "updated content"
        
        # 4. Delete a special file
        response = client.delete(f"/api/v1/special-files/{file_id}")
        assert response.status_code == 200
        
        # 5. Verify file count decreased
        response = client.get(f"/api/v1/special-files?repository_id={test_repository.id}")
        assert response.status_code == 200
        files = response.json()
        assert len(files) == 2

    @patch('app.services.github_service.GitHubService._sync_commits')
    def test_commits_sync_workflow(self, mock_sync_commits, client: TestClient, 
                                 test_repository: Repository, db_session: Session):
        """Test commits synchronization workflow."""
        
        # Mock GitHub service to return commits
        def mock_sync_side_effect(repository, limit=100):
            from datetime import datetime, timedelta
            
            # Create commits in database
            for i in range(3):
                commit = Commit(
                    repository_id=repository.id,
                    sha=f"commit{i}abc123",
                    message=f"Commit message {i}",
                    author=f"Author {i}",
                    author_email=f"author{i}@example.com",
                    committed_date=datetime.utcnow() - timedelta(days=i)
                )
                db_session.add(commit)
            db_session.commit()
        
        mock_sync_commits.side_effect = mock_sync_side_effect
        
        # 1. Sync commits
        response = client.post(f"/api/v1/commits/sync/{test_repository.id}")
        assert response.status_code == 200
        synced_commits = response.json()
        assert len(synced_commits) == 3
        
        # 2. Verify commits were created
        response = client.get(f"/api/v1/commits?repository_id={test_repository.id}")
        assert response.status_code == 200
        commits = response.json()
        assert len(commits) == 3
        
        # 3. Test commit filtering by author
        response = client.get(f"/api/v1/commits?repository_id={test_repository.id}&author=Author 0")
        assert response.status_code == 200
        filtered_commits = response.json()
        assert len(filtered_commits) == 1
        assert filtered_commits[0]["author"] == "Author 0"
        
        # 4. Test commit analytics
        response = client.get("/api/v1/commits/analytics")
        assert response.status_code == 200
        analytics = response.json()
        assert "total_commits" in analytics
        assert analytics["total_commits"] >= 3

    def test_milestone_management_workflow(self, client: TestClient, test_repository: Repository):
        """Test milestone management workflow."""
        
        # 1. Create milestone
        milestone_data = {
            "repository_id": test_repository.id,
            "title": "Version 2.0",
            "description": "Second major release",
            "due_date": "2024-12-31T23:59:59Z"
        }
        response = client.post("/api/v1/milestones", json=milestone_data)
        assert response.status_code == 201
        milestone = response.json()
        milestone_id = milestone["id"]
        
        # 2. Verify milestone was created
        response = client.get(f"/api/v1/milestones/{milestone_id}")
        assert response.status_code == 200
        assert response.json()["title"] == "Version 2.0"
        assert response.json()["is_completed"] is False
        
        # 3. Update milestone
        update_data = {"description": "Updated description"}
        response = client.put(f"/api/v1/milestones/{milestone_id}", json=update_data)
        assert response.status_code == 200
        assert response.json()["description"] == "Updated description"
        
        # 4. Complete milestone
        response = client.post(f"/api/v1/milestones/{milestone_id}/complete")
        assert response.status_code == 200
        completed_milestone = response.json()
        assert completed_milestone["is_completed"] is True
        assert completed_milestone["completed_date"] is not None
        
        # 5. Reopen milestone
        response = client.post(f"/api/v1/milestones/{milestone_id}/reopen")
        assert response.status_code == 200
        reopened_milestone = response.json()
        assert reopened_milestone["is_completed"] is False
        assert reopened_milestone["completed_date"] is None
        
        # 6. Get milestone analytics
        response = client.get("/api/v1/milestones/analytics")
        assert response.status_code == 200
        analytics = response.json()
        assert "total_milestones" in analytics
        assert "completed_milestones" in analytics
