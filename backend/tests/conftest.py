"""
Pytest configuration and fixtures
"""
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import Base, get_db
from app.models.user import User
from app.models.repository import Repository, SpecialFile, Commit, Milestone
from app.core.security import create_access_token


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session() -> Generator[Session, None, None]:
    """Create a fresh database session for each test."""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session: Session) -> Generator[TestClient, None, None]:
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
def test_user(db_session: Session) -> User:
    """Create a test user."""
    user = User(
        username="testuser",
        email="<EMAIL>",
        github_id="123456",
        github_token="test_token",
        is_active=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_repository(db_session: Session) -> Repository:
    """Create a test repository."""
    repository = Repository(
        name="test-repo",
        full_name="testuser/test-repo",
        description="A test repository",
        url="https://api.github.com/repos/testuser/test-repo",
        html_url="https://github.com/testuser/test-repo",
        clone_url="https://github.com/testuser/test-repo.git",
        default_branch="main",
        stars=10,
        forks=5,
        open_issues=2,
        is_active=True
    )
    db_session.add(repository)
    db_session.commit()
    db_session.refresh(repository)
    return repository


@pytest.fixture
def auth_headers(test_user: User) -> dict:
    """Create authentication headers for test user."""
    access_token = create_access_token(data={"sub": test_user.id})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def test_special_file(db_session: Session, test_repository: Repository) -> SpecialFile:
    """Create a test special file."""
    special_file = SpecialFile(
        repository_id=test_repository.id,
        file_type="dockerwrapper",
        file_path=".dockerwrapper",
        content="test content"
    )
    db_session.add(special_file)
    db_session.commit()
    db_session.refresh(special_file)
    return special_file


@pytest.fixture
def test_commit(db_session: Session, test_repository: Repository) -> Commit:
    """Create a test commit."""
    from datetime import datetime
    commit = Commit(
        repository_id=test_repository.id,
        sha="abc123def456",
        message="Test commit message",
        author="Test Author",
        author_email="<EMAIL>",
        committed_date=datetime.utcnow()
    )
    db_session.add(commit)
    db_session.commit()
    db_session.refresh(commit)
    return commit


@pytest.fixture
def test_milestone(db_session: Session, test_repository: Repository) -> Milestone:
    """Create a test milestone."""
    from datetime import datetime, timedelta
    milestone = Milestone(
        repository_id=test_repository.id,
        title="Test Milestone",
        description="A test milestone",
        due_date=datetime.utcnow() + timedelta(days=30),
        is_completed=False
    )
    db_session.add(milestone)
    db_session.commit()
    db_session.refresh(milestone)
    return milestone
