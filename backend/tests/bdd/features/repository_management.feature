Feature: Repository Management
  As a developer
  I want to manage GitHub repositories
  So that I can track their timeline and special files

  Background:
    Given the API is running
    And I have a valid GitHub token

  Scenario: Add a new repository
    Given I have a GitHub repository "user/test-repo"
    When I add the repository to DashDevil
    Then the repository should be tracked
    And the repository details should be fetched from GitHub
    And the repository should appear in my repository list

  Scenario: Sync repository data
    Given I have a tracked repository "user/test-repo"
    When I sync the repository data
    Then the repository information should be updated
    And the special files should be synced
    And the commits should be synced

  <PERSON>ena<PERSON>: Soft delete a repository
    Given I have a tracked repository "user/test-repo"
    When I delete the repository
    Then the repository should be soft deleted
    And the repository should not appear in the active list
    But the repository should appear in the trash

  Scenario: Restore a deleted repository
    Given I have a soft deleted repository "user/test-repo"
    When I restore the repository
    Then the repository should be active again
    And the repository should appear in my repository list
    And the repository should not appear in the trash

  Scenario: Permanently delete a repository
    Given I have a soft deleted repository "user/test-repo"
    When I permanently delete the repository
    Then the repository should be completely removed
    And all associated data should be deleted

  Scenario: Track special configuration files
    Given I have a tracked repository "user/test-repo"
    And the repository has special files
    When I sync the special files
    Then the special files should be tracked
    And I should be able to view their content
    And I should be able to edit their content

  Scenario: Monitor repository milestones
    Given I have a tracked repository "user/test-repo"
    When I create a milestone "Version 1.0"
    Then the milestone should be created
    And the milestone should have a due date
    When I complete the milestone
    Then the milestone should be marked as completed
    And the completion date should be recorded

  Scenario: View repository analytics
    Given I have a tracked repository "user/test-repo"
    And the repository has commits and special files
    When I view the repository analytics
    Then I should see commit frequency data
    And I should see author contribution data
    And I should see special files distribution
    And I should be able to export the data
