"""
BDD step definitions for repository management
"""
import pytest
from pytest_bdd import scenarios, given, when, then, parsers
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import patch, MagicMock

from app.models.repository import Repository, SpecialFile, Commit, Milestone
from app.models.user import User

# Load scenarios from feature file
scenarios('../features/repository_management.feature')


@pytest.fixture
def context():
    """Shared context for BDD tests."""
    return {}


@given("the API is running")
def api_running(client: TestClient):
    """Verify the API is running."""
    response = client.get("/health")
    assert response.status_code == 200


@given("I have a valid GitHub token")
def valid_github_token(context, test_user: User):
    """Set up a valid GitHub token."""
    context['user'] = test_user
    context['github_token'] = test_user.github_token


@given(parsers.parse('I have a GitHub repository "{repo_name}"'))
def github_repository_exists(context, repo_name: str):
    """Mock a GitHub repository."""
    context['repo_name'] = repo_name
    context['github_repo_data'] = {
        'name': repo_name.split('/')[-1],
        'full_name': repo_name,
        'description': f'Test repository {repo_name}',
        'html_url': f'https://github.com/{repo_name}',
        'clone_url': f'https://github.com/{repo_name}.git',
        'default_branch': 'main',
        'stargazers_count': 10,
        'forks_count': 2,
        'open_issues_count': 1
    }


@given(parsers.parse('I have a tracked repository "{repo_name}"'))
def tracked_repository(context, db_session: Session, repo_name: str):
    """Create a tracked repository."""
    repository = Repository(
        name=repo_name.split('/')[-1],
        full_name=repo_name,
        description=f'Test repository {repo_name}',
        url=f'https://api.github.com/repos/{repo_name}',
        html_url=f'https://github.com/{repo_name}',
        clone_url=f'https://github.com/{repo_name}.git',
        default_branch='main',
        stars=10,
        forks=2,
        open_issues=1,
        is_active=True
    )
    db_session.add(repository)
    db_session.commit()
    db_session.refresh(repository)
    
    context['repository'] = repository
    context['repo_name'] = repo_name


@given(parsers.parse('I have a soft deleted repository "{repo_name}"'))
def soft_deleted_repository(context, db_session: Session, repo_name: str):
    """Create a soft deleted repository."""
    repository = Repository(
        name=repo_name.split('/')[-1],
        full_name=repo_name,
        description=f'Test repository {repo_name}',
        url=f'https://api.github.com/repos/{repo_name}',
        html_url=f'https://github.com/{repo_name}',
        clone_url=f'https://github.com/{repo_name}.git',
        default_branch='main',
        is_active=True
    )
    repository.soft_delete()
    db_session.add(repository)
    db_session.commit()
    db_session.refresh(repository)
    
    context['repository'] = repository
    context['repo_name'] = repo_name


@given("the repository has special files")
def repository_has_special_files(context):
    """Mock repository with special files."""
    context['special_files'] = [
        {'file_type': 'dockerwrapper', 'file_path': '.dockerwrapper', 'content': 'wrapper config'},
        {'file_type': 'claude', 'file_path': '.claude', 'content': 'claude config'},
        {'file_type': 'cursor', 'file_path': '.cursor', 'content': 'cursor config'}
    ]


@given("the repository has commits and special files")
def repository_has_data(context, db_session: Session):
    """Add commits and special files to repository."""
    repository = context['repository']
    
    # Add commits
    from datetime import datetime, timedelta
    for i in range(5):
        commit = Commit(
            repository_id=repository.id,
            sha=f'commit{i}abc123',
            message=f'Commit message {i}',
            author=f'Author {i % 2}',  # Two different authors
            committed_date=datetime.utcnow() - timedelta(days=i)
        )
        db_session.add(commit)
    
    # Add special files
    for file_type in ['dockerwrapper', 'claude', 'cursor']:
        special_file = SpecialFile(
            repository_id=repository.id,
            file_type=file_type,
            file_path=f'.{file_type}',
            content=f'{file_type} configuration'
        )
        db_session.add(special_file)
    
    db_session.commit()


@when("I add the repository to DashDevil")
@patch('app.services.github_service.GitHubService.sync_repository')
def add_repository(mock_sync, context, client: TestClient, db_session: Session):
    """Add repository to DashDevil."""
    # Mock the GitHub service
    repo_data = context['github_repo_data']
    mock_repo = Repository(**{
        'name': repo_data['name'],
        'full_name': repo_data['full_name'],
        'description': repo_data['description'],
        'url': f"https://api.github.com/repos/{repo_data['full_name']}",
        'html_url': repo_data['html_url'],
        'clone_url': repo_data['clone_url'],
        'default_branch': repo_data['default_branch'],
        'stars': repo_data['stargazers_count'],
        'forks': repo_data['forks_count'],
        'open_issues': repo_data['open_issues_count']
    })
    mock_repo.id = 1
    mock_sync.return_value = mock_repo
    
    # Make API call
    response = client.post("/api/v1/repositories", json={"full_name": context['repo_name']})
    context['response'] = response
    
    if response.status_code == 201:
        context['repository'] = db_session.query(Repository).filter(
            Repository.full_name == context['repo_name']
        ).first()


@when("I sync the repository data")
@patch('app.services.github_service.GitHubService.sync_repository')
def sync_repository_data(mock_sync, context, client: TestClient):
    """Sync repository data."""
    repository = context['repository']
    mock_sync.return_value = repository
    
    response = client.post(f"/api/v1/repositories/{repository.id}/sync")
    context['response'] = response


@when("I delete the repository")
def delete_repository(context, client: TestClient):
    """Soft delete the repository."""
    repository = context['repository']
    response = client.delete(f"/api/v1/repositories/{repository.id}")
    context['response'] = response


@when("I restore the repository")
def restore_repository(context, client: TestClient):
    """Restore the repository."""
    repository = context['repository']
    response = client.post(f"/api/v1/repositories/{repository.id}/restore")
    context['response'] = response


@when("I permanently delete the repository")
def permanently_delete_repository(context, client: TestClient):
    """Permanently delete the repository."""
    repository = context['repository']
    response = client.delete(f"/api/v1/repositories/{repository.id}?permanent=true")
    context['response'] = response


@when("I sync the special files")
@patch('app.services.github_service.GitHubService._sync_special_files')
def sync_special_files(mock_sync, context, client: TestClient, db_session: Session):
    """Sync special files."""
    repository = context['repository']
    
    # Mock sync to create special files
    def mock_sync_side_effect(repo):
        for file_data in context['special_files']:
            special_file = SpecialFile(
                repository_id=repo.id,
                **file_data
            )
            db_session.add(special_file)
        db_session.commit()
    
    mock_sync.side_effect = mock_sync_side_effect
    
    response = client.post(f"/api/v1/special-files/sync/{repository.id}")
    context['response'] = response


@when(parsers.parse('I create a milestone "{milestone_title}"'))
def create_milestone(context, client: TestClient, milestone_title: str):
    """Create a milestone."""
    repository = context['repository']
    milestone_data = {
        "repository_id": repository.id,
        "title": milestone_title,
        "description": f"Milestone: {milestone_title}",
        "due_date": "2024-12-31T23:59:59Z"
    }
    
    response = client.post("/api/v1/milestones", json=milestone_data)
    context['response'] = response
    
    if response.status_code == 201:
        context['milestone'] = response.json()


@when("I complete the milestone")
def complete_milestone(context, client: TestClient):
    """Complete the milestone."""
    milestone = context['milestone']
    response = client.post(f"/api/v1/milestones/{milestone['id']}/complete")
    context['response'] = response


@when("I view the repository analytics")
def view_repository_analytics(context, client: TestClient):
    """View repository analytics."""
    repository = context['repository']
    response = client.get(f"/api/v1/repositories/{repository.id}/analytics/files")
    context['analytics_response'] = response


@then("the repository should be tracked")
def repository_tracked(context):
    """Verify repository is tracked."""
    assert context['response'].status_code == 201
    assert context['repository'] is not None


@then("the repository details should be fetched from GitHub")
def repository_details_fetched(context):
    """Verify repository details were fetched."""
    response_data = context['response'].json()
    github_data = context['github_repo_data']
    
    assert response_data['name'] == github_data['name']
    assert response_data['full_name'] == github_data['full_name']
    assert response_data['description'] == github_data['description']


@then("the repository should appear in my repository list")
def repository_in_list(context, client: TestClient):
    """Verify repository appears in list."""
    response = client.get("/api/v1/repositories")
    assert response.status_code == 200
    
    repositories = response.json()
    repo_names = [repo['full_name'] for repo in repositories]
    assert context['repo_name'] in repo_names


@then("the repository information should be updated")
def repository_info_updated(context):
    """Verify repository information was updated."""
    assert context['response'].status_code == 200


@then("the special files should be synced")
def special_files_synced(context, client: TestClient):
    """Verify special files were synced."""
    repository = context['repository']
    response = client.get(f"/api/v1/special-files?repository_id={repository.id}")
    assert response.status_code == 200
    
    files = response.json()
    assert len(files) > 0


@then("the commits should be synced")
def commits_synced(context, client: TestClient):
    """Verify commits were synced."""
    repository = context['repository']
    response = client.get(f"/api/v1/commits?repository_id={repository.id}")
    assert response.status_code == 200


@then("the repository should be soft deleted")
def repository_soft_deleted(context, db_session: Session):
    """Verify repository is soft deleted."""
    assert context['response'].status_code == 200
    
    db_session.refresh(context['repository'])
    assert context['repository'].is_deleted is True


@then("the repository should not appear in the active list")
def repository_not_in_active_list(context, client: TestClient):
    """Verify repository doesn't appear in active list."""
    response = client.get("/api/v1/repositories")
    assert response.status_code == 200
    
    repositories = response.json()
    repo_names = [repo['full_name'] for repo in repositories]
    assert context['repo_name'] not in repo_names


@then("the repository should appear in the trash")
def repository_in_trash(context, client: TestClient):
    """Verify repository appears in trash."""
    response = client.get("/api/v1/repositories/deleted")
    assert response.status_code == 200
    
    repositories = response.json()
    repo_names = [repo['full_name'] for repo in repositories]
    assert context['repo_name'] in repo_names
