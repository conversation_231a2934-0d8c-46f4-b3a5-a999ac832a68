# API settings
PROJECT_NAME=RepoTimeline
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=11520  # 8 days

# CORS settings
FRONTEND_URL=http://localhost:3000

# Database settings
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=repotimeline

# GitHub API settings
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GITHUB_WEBHOOK_SECRET=your-github-webhook-secret

# Scheduler settings
SCHEDULER_JOBS_INTERVAL=60  # minutes 