#!/bin/bash

# DashDevil Test Runner Script
# This script runs all tests for both backend and frontend

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run backend tests
run_backend_tests() {
    print_status "Running backend tests..."
    
    cd backend
    
    # Check if pytest is available
    if ! command_exists pytest; then
        print_error "pytest not found. Installing dependencies..."
        pip install -r requirements.txt
    fi
    
    # Run unit tests
    print_status "Running backend unit tests..."
    pytest tests/unit/ -v --cov=app --cov-report=term-missing --cov-report=html:htmlcov/unit
    
    # Run integration tests
    print_status "Running backend integration tests..."
    pytest tests/integration/ -v --cov=app --cov-append --cov-report=term-missing --cov-report=html:htmlcov/integration
    
    # Run BDD tests
    print_status "Running backend BDD tests..."
    pytest tests/bdd/ -v --cov=app --cov-append --cov-report=term-missing --cov-report=html:htmlcov/bdd
    
    # Generate combined coverage report
    print_status "Generating combined backend coverage report..."
    pytest --cov=app --cov-report=term-missing --cov-report=html:htmlcov/combined --cov-report=xml:coverage.xml tests/
    
    cd ..
    print_success "Backend tests completed!"
}

# Function to run frontend tests
run_frontend_tests() {
    print_status "Running frontend tests..."
    
    cd frontend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_error "node_modules not found. Installing dependencies..."
        npm install
    fi
    
    # Run unit tests
    print_status "Running frontend unit tests..."
    npm run test -- --coverage --watchAll=false --testPathPattern="unit"
    
    # Run integration tests
    print_status "Running frontend integration tests..."
    npm run test -- --coverage --watchAll=false --testPathPattern="integration"
    
    # Run type checking
    print_status "Running TypeScript type checking..."
    npm run typecheck
    
    # Run linting
    print_status "Running ESLint..."
    npm run lint
    
    cd ..
    print_success "Frontend tests completed!"
}

# Function to run E2E tests
run_e2e_tests() {
    print_status "Running end-to-end tests..."
    
    cd frontend
    
    # Check if Playwright is installed
    if ! command_exists playwright; then
        print_error "Playwright not found. Installing..."
        npx playwright install
    fi
    
    # Start the development server in background
    print_status "Starting development server..."
    npm start &
    SERVER_PID=$!
    
    # Wait for server to be ready
    print_status "Waiting for server to be ready..."
    sleep 30
    
    # Run Playwright tests
    print_status "Running Playwright E2E tests..."
    npm run test:e2e
    
    # Kill the development server
    kill $SERVER_PID
    
    cd ..
    print_success "E2E tests completed!"
}

# Function to run all tests
run_all_tests() {
    print_status "Running all tests for DashDevil..."
    
    # Create test results directory
    mkdir -p test-results
    
    # Run backend tests
    run_backend_tests 2>&1 | tee test-results/backend-tests.log
    
    # Run frontend tests
    run_frontend_tests 2>&1 | tee test-results/frontend-tests.log
    
    # Run E2E tests
    run_e2e_tests 2>&1 | tee test-results/e2e-tests.log
    
    print_success "All tests completed! Check test-results/ directory for detailed logs."
}

# Function to run specific test type
run_specific_tests() {
    case $1 in
        "backend")
            run_backend_tests
            ;;
        "frontend")
            run_frontend_tests
            ;;
        "e2e")
            run_e2e_tests
            ;;
        "unit")
            print_status "Running unit tests only..."
            cd backend && pytest tests/unit/ -v
            cd ../frontend && npm run test -- --watchAll=false --testPathPattern="unit"
            cd ..
            ;;
        "integration")
            print_status "Running integration tests only..."
            cd backend && pytest tests/integration/ -v
            cd ../frontend && npm run test -- --watchAll=false --testPathPattern="integration"
            cd ..
            ;;
        "bdd")
            print_status "Running BDD tests only..."
            cd backend && pytest tests/bdd/ -v
            cd ..
            ;;
        *)
            print_error "Unknown test type: $1"
            print_status "Available options: backend, frontend, e2e, unit, integration, bdd, all"
            exit 1
            ;;
    esac
}

# Function to show coverage report
show_coverage() {
    print_status "Opening coverage reports..."
    
    # Backend coverage
    if [ -f "backend/htmlcov/combined/index.html" ]; then
        print_status "Backend coverage report: backend/htmlcov/combined/index.html"
        if command_exists open; then
            open backend/htmlcov/combined/index.html
        elif command_exists xdg-open; then
            xdg-open backend/htmlcov/combined/index.html
        fi
    fi
    
    # Frontend coverage
    if [ -f "frontend/coverage/lcov-report/index.html" ]; then
        print_status "Frontend coverage report: frontend/coverage/lcov-report/index.html"
        if command_exists open; then
            open frontend/coverage/lcov-report/index.html
        elif command_exists xdg-open; then
            xdg-open frontend/coverage/lcov-report/index.html
        fi
    fi
}

# Function to clean test artifacts
clean_test_artifacts() {
    print_status "Cleaning test artifacts..."
    
    # Backend
    rm -rf backend/htmlcov/
    rm -rf backend/.coverage
    rm -rf backend/coverage.xml
    rm -rf backend/.pytest_cache/
    
    # Frontend
    rm -rf frontend/coverage/
    rm -rf frontend/test-results/
    rm -rf frontend/playwright-report/
    
    # Root
    rm -rf test-results/
    
    print_success "Test artifacts cleaned!"
}

# Main script logic
case "${1:-all}" in
    "all")
        run_all_tests
        ;;
    "clean")
        clean_test_artifacts
        ;;
    "coverage")
        show_coverage
        ;;
    *)
        run_specific_tests $1
        ;;
esac

print_success "Test script completed!"
