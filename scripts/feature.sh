#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options] FEATURE_NAME"
    echo "Options:"
    echo "  --start-api      Start API development for the feature"
    echo "  --start-api-test Start API test development for the feature"
    echo "  --start-ui       Start UI development for the feature"
    echo "  --start-e2e      Start E2E test development for the feature"
    echo "  --complete-api   Mark API development as complete"
    echo "  --complete-api-test Mark API testing as complete"
    echo "  --complete-ui    Mark UI development as complete"
    echo "  --complete-e2e   Mark E2E testing as complete"
    echo "  --status         Show feature development status"
    echo "  --help           Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --start-api repository-timeline"
    echo "  $0 --complete-api repository-timeline"
    echo "  $0 --status"
}

# Function to update feature status
function update_status {
    local feature=$1
    local phase=$2
    local status=$3
    
    # Convert feature name to title case
    feature_title=$(echo $feature | sed -E 's/(^|-)([a-z])/\1\u\2/g' | sed 's/-/ /g')
    
    # Check if feature exists in migration tracking table
    if ! grep -q "| $feature_title " docs/roadmap.md; then
        # Add new feature to the table
        sed -i "/| Mobile Responsiveness .*/a | $feature_title | Not Started | Not Started | Not Started | Not Started | - |" docs/roadmap.md
        echo "Added new feature: $feature_title to the roadmap"
    fi
    
    # Update status for the specified phase
    case $phase in
        api)
            sed -i "s/| $feature_title | [^|]* | /| $feature_title | $status | /" docs/roadmap.md
            ;;
        api-test)
            sed -i "s/| $feature_title | [^|]* | [^|]* | /| $feature_title | [^|]* | $status | /" docs/roadmap.md
            ;;
        ui)
            sed -i "s/| $feature_title | [^|]* | [^|]* | [^|]* | /| $feature_title | [^|]* | [^|]* | $status | /" docs/roadmap.md
            ;;
        e2e)
            sed -i "s/| $feature_title | [^|]* | [^|]* | [^|]* | [^|]* | /| $feature_title | [^|]* | [^|]* | [^|]* | $status | /" docs/roadmap.md
            ;;
    esac
    
    # If e2e is complete, update completion date
    if [ "$phase" == "e2e" ] && [ "$status" == "Complete" ]; then
        current_date=$(date +"%Y-%m-%d")
        sed -i "s/| $feature_title | [^|]* | [^|]* | [^|]* | [^|]* | [^|]* |/| $feature_title | [^|]* | [^|]* | [^|]* | [^|]* | $current_date |/" docs/roadmap.md
    fi
    
    echo "Updated status for $feature_title $phase to $status"
}

# Function to show feature status
function show_status {
    echo "Feature Development Status:"
    echo "-------------------------"
    grep -A 100 "## Migration Tracking" docs/roadmap.md | grep "|" | head -n -1
}

# Function to create API files
function create_api_files {
    local feature=$1
    local feature_snake=$(echo $feature | tr '-' '_')
    
    # Create API endpoint file
    mkdir -p backend/app/api/api_v1/endpoints
    
    if [ ! -f "backend/app/api/api_v1/endpoints/${feature_snake}.py" ]; then
        cat > "backend/app/api/api_v1/endpoints/${feature_snake}.py" << EOF
"""
API endpoints for ${feature} feature
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.${feature_snake} import ${feature_snake^}
from app.schemas.${feature_snake} import ${feature_snake^}Create, ${feature_snake^}Update, ${feature_snake^}Response

router = APIRouter(
    prefix="/${feature_snake}",
    tags=["${feature}"],
    responses={404: {"description": "${feature} not found"}},
)

@router.get("/", response_model=List[${feature_snake^}Response])
async def get_all_${feature_snake}s(
    skip: int = 0, 
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all ${feature}s
    """
    items = db.query(${feature_snake^}).offset(skip).limit(limit).all()
    return items

@router.get("/{${feature_snake}_id}", response_model=${feature_snake^}Response)
async def get_${feature_snake}(
    ${feature_snake}_id: int,
    db: Session = Depends(get_db)
):
    """
    Get a specific ${feature} by ID
    """
    item = db.query(${feature_snake^}).filter(${feature_snake^}.id == ${feature_snake}_id).first()
    if item is None:
        raise HTTPException(status_code=404, detail="${feature} not found")
    return item

@router.post("/", response_model=${feature_snake^}Response, status_code=status.HTTP_201_CREATED)
async def create_${feature_snake}(
    ${feature_snake}_create: ${feature_snake^}Create,
    db: Session = Depends(get_db)
):
    """
    Create a new ${feature}
    """
    db_item = ${feature_snake^}(**${feature_snake}_create.dict())
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

@router.put("/{${feature_snake}_id}", response_model=${feature_snake^}Response)
async def update_${feature_snake}(
    ${feature_snake}_id: int,
    ${feature_snake}_update: ${feature_snake^}Update,
    db: Session = Depends(get_db)
):
    """
    Update a ${feature}
    """
    db_item = db.query(${feature_snake^}).filter(${feature_snake^}.id == ${feature_snake}_id).first()
    if db_item is None:
        raise HTTPException(status_code=404, detail="${feature} not found")
    
    update_data = ${feature_snake}_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_item, key, value)
    
    db.commit()
    db.refresh(db_item)
    return db_item

@router.delete("/{${feature_snake}_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_${feature_snake}(
    ${feature_snake}_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete a ${feature}
    """
    db_item = db.query(${feature_snake^}).filter(${feature_snake^}.id == ${feature_snake}_id).first()
    if db_item is None:
        raise HTTPException(status_code=404, detail="${feature} not found")
    
    db.delete(db_item)
    db.commit()
EOF
        echo "Created API endpoint file: backend/app/api/api_v1/endpoints/${feature_snake}.py"
    else
        echo "API endpoint file already exists: backend/app/api/api_v1/endpoints/${feature_snake}.py"
    fi
    
    # Create model file
    mkdir -p backend/app/models
    
    if [ ! -f "backend/app/models/${feature_snake}.py" ]; then
        cat > "backend/app/models/${feature_snake}.py" << EOF
"""
Database models for ${feature} feature
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime

from app.core.database import Base


class ${feature_snake^}(Base):
    """
    Model for ${feature} data
    """
    __tablename__ = "${feature_snake}s"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(Text, nullable=True)
    # TODO: Add additional fields as needed
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
EOF
        echo "Created model file: backend/app/models/${feature_snake}.py"
    else
        echo "Model file already exists: backend/app/models/${feature_snake}.py"
    fi
    
    # Create schema file
    mkdir -p backend/app/schemas
    
    if [ ! -f "backend/app/schemas/${feature_snake}.py" ]; then
        cat > "backend/app/schemas/${feature_snake}.py" << EOF
"""
Pydantic schemas for ${feature} feature
"""
from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class ${feature_snake^}Base(BaseModel):
    """Base ${feature} schema with common attributes"""
    name: str
    description: Optional[str] = None


class ${feature_snake^}Create(${feature_snake^}Base):
    """Schema for creating a new ${feature}"""
    pass


class ${feature_snake^}Update(BaseModel):
    """Schema for updating a ${feature}"""
    name: Optional[str] = None
    description: Optional[str] = None


class ${feature_snake^}Response(${feature_snake^}Base):
    """Schema for ${feature} responses"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True
EOF
        echo "Created schema file: backend/app/schemas/${feature_snake}.py"
    else
        echo "Schema file already exists: backend/app/schemas/${feature_snake}.py"
    fi

    # Update API router
    if ! grep -q "from app.api.api_v1.endpoints.${feature_snake} import router as ${feature_snake}_router" backend/app/api/api_v1/api.py; then
        # Check if api.py exists, if not create it
        mkdir -p backend/app/api/api_v1
        if [ ! -f "backend/app/api/api_v1/api.py" ]; then
            cat > "backend/app/api/api_v1/api.py" << EOF
"""
API router configuration
"""
from fastapi import APIRouter

api_router = APIRouter()

# Import and include routers from endpoints
from app.api.api_v1.endpoints.${feature_snake} import router as ${feature_snake}_router

api_router.include_router(${feature_snake}_router)
EOF
        else
            # Add import statement
            sed -i "/^from fastapi import APIRouter/a from app.api.api_v1.endpoints.${feature_snake} import router as ${feature_snake}_router" backend/app/api/api_v1/api.py
            # Add router inclusion
            sed -i "/^api_router = APIRouter()/a api_router.include_router(${feature_snake}_router)" backend/app/api/api_v1/api.py
        fi
        echo "Updated API router to include ${feature} endpoints"
    fi
}

# Function to create API test files
function create_api_test_files {
    local feature=$1
    local feature_snake=$(echo $feature | tr '-' '_')
    
    # Create test file
    mkdir -p backend/tests
    
    if [ ! -f "backend/tests/test_${feature_snake}_api.py" ]; then
        cat > "backend/tests/test_${feature_snake}_api.py" << EOF
"""
API tests for ${feature} feature
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.${feature_snake} import ${feature_snake^}
from app.core.database import Base, get_db
from tests.utils.database import TestingSessionLocal, engine


# Set up the database for testing
Base.metadata.create_all(bind=engine)

# Override the get_db dependency
@pytest.fixture
def db():
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


# Override the get_db dependency in the app
def override_get_db():
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


def test_create_${feature_snake}():
    """Test creating a ${feature}"""
    ${feature_snake}_data = {
        "name": "Test ${feature}",
        "description": "Test description for ${feature}"
    }
    response = client.post("/api/v1/${feature_snake}/", json=${feature_snake}_data)
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == ${feature_snake}_data["name"]
    assert data["description"] == ${feature_snake}_data["description"]
    assert "id" in data
    
    # Clean up
    created_id = data["id"]
    client.delete(f"/api/v1/${feature_snake}/{created_id}")


def test_get_${feature_snake}():
    """Test getting a specific ${feature}"""
    # Create a test ${feature}
    ${feature_snake}_data = {
        "name": "Test ${feature} for Get",
        "description": "Test description for get"
    }
    create_response = client.post("/api/v1/${feature_snake}/", json=${feature_snake}_data)
    created_id = create_response.json()["id"]
    
    # Get the ${feature}
    response = client.get(f"/api/v1/${feature_snake}/{created_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == ${feature_snake}_data["name"]
    assert data["description"] == ${feature_snake}_data["description"]
    
    # Clean up
    client.delete(f"/api/v1/${feature_snake}/{created_id}")


def test_update_${feature_snake}():
    """Test updating a ${feature}"""
    # Create a test ${feature}
    ${feature_snake}_data = {
        "name": "Test ${feature} for Update",
        "description": "Test description for update"
    }
    create_response = client.post("/api/v1/${feature_snake}/", json=${feature_snake}_data)
    created_id = create_response.json()["id"]
    
    # Update the ${feature}
    update_data = {
        "name": "Updated ${feature}",
        "description": "Updated description"
    }
    response = client.put(f"/api/v1/${feature_snake}/{created_id}", json=update_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["description"] == update_data["description"]
    
    # Clean up
    client.delete(f"/api/v1/${feature_snake}/{created_id}")


def test_delete_${feature_snake}():
    """Test deleting a ${feature}"""
    # Create a test ${feature}
    ${feature_snake}_data = {
        "name": "Test ${feature} for Delete",
        "description": "Test description for delete"
    }
    create_response = client.post("/api/v1/${feature_snake}/", json=${feature_snake}_data)
    created_id = create_response.json()["id"]
    
    # Delete the ${feature}
    response = client.delete(f"/api/v1/${feature_snake}/{created_id}")
    assert response.status_code == 204
    
    # Verify deletion
    get_response = client.get(f"/api/v1/${feature_snake}/{created_id}")
    assert get_response.status_code == 404


def test_get_all_${feature_snake}s():
    """Test getting all ${feature}s"""
    # Create multiple test ${feature}s
    ${feature_snake}_data_1 = {"name": "Test ${feature} 1", "description": "Test description 1"}
    ${feature_snake}_data_2 = {"name": "Test ${feature} 2", "description": "Test description 2"}
    
    response_1 = client.post("/api/v1/${feature_snake}/", json=${feature_snake}_data_1)
    response_2 = client.post("/api/v1/${feature_snake}/", json=${feature_snake}_data_2)
    
    created_id_1 = response_1.json()["id"]
    created_id_2 = response_2.json()["id"]
    
    # Get all ${feature}s
    response = client.get("/api/v1/${feature_snake}/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 2
    
    # Verify the created ${feature}s are in the response
    created_ids = [item["id"] for item in data]
    assert created_id_1 in created_ids
    assert created_id_2 in created_ids
    
    # Clean up
    client.delete(f"/api/v1/${feature_snake}/{created_id_1}")
    client.delete(f"/api/v1/${feature_snake}/{created_id_2}")
EOF
        echo "Created API test file: backend/tests/test_${feature_snake}_api.py"
    else
        echo "API test file already exists: backend/tests/test_${feature_snake}_api.py"
    fi
}

# Function to create React component files
function create_ui_files {
    local feature=$1
    local feature_snake=$(echo $feature | tr '-' '_')
    local feature_pascal=$(echo $feature | sed -E 's/(^|-)([a-z])/\1\u\2/g' | sed 's/-//g')
    
    # Create React component files
    mkdir -p frontend/src/pages/${feature_pascal}
    
    if [ ! -f "frontend/src/pages/${feature_pascal}/index.tsx" ]; then
        cat > "frontend/src/pages/${feature_pascal}/index.tsx" << EOF
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Button, Table, Space, message, Spin } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import api from '../../services/api';
import './styles.css';

interface ${feature_pascal}Data {
  id: number;
  name: string;
  description: string;
  created_at: string;
  updated_at: string | null;
}

const ${feature_pascal}Page: React.FC = () => {
  const [data, setData] = useState<${feature_pascal}Data[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/v1/${feature_snake}/');
      setData(response.data);
    } catch (error) {
      console.error('Error fetching ${feature} data:', error);
      message.error('Failed to load ${feature} data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    navigate('/${feature}/create');
  };

  const handleEdit = (id: number) => {
    navigate(\`/${feature}/edit/\${id}\`);
  };

  const handleDelete = async (id: number) => {
    try {
      await api.delete(\`/api/v1/${feature_snake}/\${id}\`);
      message.success('${feature_pascal} deleted successfully');
      fetchData();
    } catch (error) {
      console.error('Error deleting ${feature}:', error);
      message.error('Failed to delete ${feature}');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: ${feature_pascal}Data) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record.id)}
          >
            Edit
          </Button>
          <Button 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          >
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="${feature}-container">
      <Card 
        title="${feature_pascal} Management" 
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleCreate}
          >
            Create New
          </Button>
        }
      >
        {loading ? (
          <div className="loading-container">
            <Spin size="large" />
          </div>
        ) : (
          <Table 
            dataSource={data} 
            columns={columns} 
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        )}
      </Card>
    </div>
  );
};

export default ${feature_pascal}Page;
EOF
        echo "Created React page component: frontend/src/pages/${feature_pascal}/index.tsx"
    else
        echo "React page component already exists: frontend/src/pages/${feature_pascal}/index.tsx"
    fi
    
    if [ ! -f "frontend/src/pages/${feature_pascal}/${feature_pascal}Form.tsx" ]; then
        cat > "frontend/src/pages/${feature_pascal}/${feature_pascal}Form.tsx" << EOF
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Card, message, Spin } from 'antd';
import api from '../../services/api';

interface ${feature_pascal}FormData {
  name: string;
  description: string;
}

const ${feature_pascal}Form: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [initializing, setInitializing] = useState<boolean>(false);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  useEffect(() => {
    if (isEditMode) {
      fetchData();
    }
  }, [id]);

  const fetchData = async () => {
    try {
      setInitializing(true);
      const response = await api.get(\`/api/v1/${feature_snake}/\${id}\`);
      form.setFieldsValue({
        name: response.data.name,
        description: response.data.description,
      });
    } catch (error) {
      console.error('Error fetching ${feature} data:', error);
      message.error('Failed to load ${feature} data');
      navigate('/${feature}');
    } finally {
      setInitializing(false);
    }
  };

  const onFinish = async (values: ${feature_pascal}FormData) => {
    try {
      setLoading(true);
      if (isEditMode) {
        await api.put(\`/api/v1/${feature_snake}/\${id}\`, values);
        message.success('${feature_pascal} updated successfully');
      } else {
        await api.post('/api/v1/${feature_snake}/', values);
        message.success('${feature_pascal} created successfully');
      }
      navigate('/${feature}');
    } catch (error) {
      console.error(\`Error \${isEditMode ? 'updating' : 'creating'} ${feature}:\`, error);
      message.error(\`Failed to \${isEditMode ? 'update' : 'create'} ${feature}\`);
    } finally {
      setLoading(false);
    }
  };

  if (initializing) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Card title={isEditMode ? 'Edit ${feature_pascal}' : 'Create ${feature_pascal}'}>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
      >
        <Form.Item
          name="name"
          label="Name"
          rules={[{ required: true, message: 'Please enter a name' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="description"
          label="Description"
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            {isEditMode ? 'Update' : 'Create'}
          </Button>
          <Button 
            style={{ marginLeft: '10px' }} 
            onClick={() => navigate('/${feature}')}
          >
            Cancel
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default ${feature_pascal}Form;
EOF
        echo "Created React form component: frontend/src/pages/${feature_pascal}/${feature_pascal}Form.tsx"
    else
        echo "React form component already exists: frontend/src/pages/${feature_pascal}/${feature_pascal}Form.tsx"
    fi
    
    if [ ! -f "frontend/src/pages/${feature_pascal}/styles.css" ]; then
        cat > "frontend/src/pages/${feature_pascal}/styles.css" << EOF
.${feature}-container {
  padding: 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
EOF
        echo "Created CSS file: frontend/src/pages/${feature_pascal}/styles.css"
    else
        echo "CSS file already exists: frontend/src/pages/${feature_pascal}/styles.css"
    fi

    # Update routes file
    mkdir -p frontend/src/routes
    
    if [ ! -f "frontend/src/routes/index.tsx" ]; then
        cat > "frontend/src/routes/index.tsx" << EOF
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Dashboard from '../pages/Dashboard';
import ${feature_pascal}Page from '../pages/${feature_pascal}';
import ${feature_pascal}Form from '../pages/${feature_pascal}/${feature_pascal}Form';
import NotFound from '../pages/NotFound';

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<Navigate to="/dashboard" />} />
      <Route path="/dashboard" element={<Dashboard />} />
      
      <Route path="/${feature}" element={<${feature_pascal}Page />} />
      <Route path="/${feature}/create" element={<${feature_pascal}Form />} />
      <Route path="/${feature}/edit/:id" element={<${feature_pascal}Form />} />
      
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default AppRoutes;
EOF
        echo "Created routes file: frontend/src/routes/index.tsx"
    else
        # Check if routes for this feature are already defined
        if ! grep -q "${feature_pascal}Page" frontend/src/routes/index.tsx; then
            # Add import statements
            sed -i "/^import React from 'react';/a import ${feature_pascal}Page from '../pages/${feature_pascal}';\nimport ${feature_pascal}Form from '../pages/${feature_pascal}/${feature_pascal}Form';" frontend/src/routes/index.tsx
            
            # Add routes
            sed -i "/\/dashboard/a \ \ \ \ \ \ <Route path=\"\/${feature}\" element={<${feature_pascal}Page \/>} \/>\n      <Route path=\"\/${feature}\/create\" element={<${feature_pascal}Form \/>} \/>\n      <Route path=\"\/${feature}\/edit\/:id\" element={<${feature_pascal}Form \/>} \/>" frontend/src/routes/index.tsx
            
            echo "Updated routes file with ${feature} routes"
        else
            echo "Routes for ${feature} already exist in routes file"
        fi
    fi
}

# Function to create E2E test files
function create_e2e_test_files {
    local feature=$1
    local feature_snake=$(echo $feature | tr '-' '_')
    local feature_pascal=$(echo $feature | sed -E 's/(^|-)([a-z])/\1\u\2/g' | sed 's/-//g')
    
    # Create Playwright test file
    mkdir -p frontend/tests/e2e
    
    if [ ! -f "frontend/tests/e2e/${feature_snake}.spec.ts" ]; then
        cat > "frontend/tests/e2e/${feature_snake}.spec.ts" << EOF
import { test, expect } from '@playwright/test';

test.describe('${feature_pascal} Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the login page and log in
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password');
    await page.click('[data-testid="login-button"]');
    
    // Wait for navigation to complete
    await page.waitForURL('/dashboard');
  });

  test('should display ${feature} list', async ({ page }) => {
    // Go to ${feature} page
    await page.goto('/${feature}');
    
    // Check that the page title is correct
    await expect(page.locator('h1')).toContainText('${feature_pascal} Management');
    
    // Check that the table is displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should create a new ${feature}', async ({ page }) => {
    // Go to create ${feature} page
    await page.goto('/${feature}/create');
    
    // Fill the form
    await page.fill('input[name="name"]', 'Test ${feature_pascal}');
    await page.fill('textarea[name="description"]', 'Test description');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for navigation and success message
    await page.waitForURL('/${feature}');
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify the new ${feature} is in the table
    await expect(page.locator('table')).toContainText('Test ${feature_pascal}');
  });

  test('should edit an existing ${feature}', async ({ page }) => {
    // Go to ${feature} page
    await page.goto('/${feature}');
    
    // Click on edit button for the first ${feature}
    await page.click('button:has-text("Edit"):first-of-type');
    
    // Wait for navigation to edit page
    await page.waitForURL('/${feature}/edit/*');
    
    // Update the form
    await page.fill('input[name="name"]', 'Updated ${feature_pascal}');
    await page.fill('textarea[name="description"]', 'Updated description');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for navigation and success message
    await page.waitForURL('/${feature}');
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify the updated ${feature} is in the table
    await expect(page.locator('table')).toContainText('Updated ${feature_pascal}');
  });

  test('should delete a ${feature}', async ({ page }) => {
    // Go to ${feature} page
    await page.goto('/${feature}');
    
    // Get the name of the first ${feature}
    const firstItemName = await page.locator('table tbody tr:first-child td:first-child').textContent();
    
    // Click on delete button for the first ${feature}
    await page.click('button:has-text("Delete"):first-of-type');
    
    // Confirm deletion in modal
    await page.click('button:has-text("OK")');
    
    // Wait for success message
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // Verify the ${feature} is no longer in the table
    if (firstItemName) {
      await expect(page.locator('table')).not.toContainText(firstItemName);
    }
  });
});
EOF
        echo "Created E2E test file: frontend/tests/e2e/${feature_snake}.spec.ts"
    else
        echo "E2E test file already exists: frontend/tests/e2e/${feature_snake}.spec.ts"
    fi
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

action=""
feature=""

while [ $# -gt 0 ]; do
    case "$1" in
        --start-api)
            action="start-api"
            shift
            ;;
        --start-api-test)
            action="start-api-test"
            shift
            ;;
        --start-ui)
            action="start-ui"
            shift
            ;;
        --start-e2e)
            action="start-e2e"
            shift
            ;;
        --complete-api)
            action="complete-api"
            shift
            ;;
        --complete-api-test)
            action="complete-api-test"
            shift
            ;;
        --complete-ui)
            action="complete-ui"
            shift
            ;;
        --complete-e2e)
            action="complete-e2e"
            shift
            ;;
        --status)
            show_status
            exit 0
            ;;
        --help)
            display_usage
            exit 0
            ;;
        -*)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
        *)
            feature=$1
            shift
            ;;
    esac
done

if [ "$action" != "" ] && [ "$feature" == "" ]; then
    echo "Error: No feature name specified"
    display_usage
    exit 1
fi

# Check if docs/roadmap.md exists
if [ ! -f "docs/roadmap.md" ]; then
    echo "Error: docs/roadmap.md file not found"
    echo "Please create the roadmap file first"
    exit 1
fi

case "$action" in
    start-api)
        update_status "$feature" "api" "In Progress"
        create_api_files "$feature"
        ;;
    start-api-test)
        update_status "$feature" "api-test" "In Progress"
        create_api_test_files "$feature"
        ;;
    start-ui)
        update_status "$feature" "ui" "In Progress"
        create_ui_files "$feature"
        ;;
    start-e2e)
        update_status "$feature" "e2e" "In Progress"
        create_e2e_test_files "$feature"
        ;;
    complete-api)
        update_status "$feature" "api" "Complete"
        ;;
    complete-api-test)
        update_status "$feature" "api-test" "Complete"
        ;;
    complete-ui)
        update_status "$feature" "ui" "Complete"
        ;;
    complete-e2e)
        update_status "$feature" "e2e" "Complete"
        ;;
    *)
        echo "Error: No action specified"
        display_usage
        exit 1
        ;;
esac 