# Repository Analytics

The RepoTimeline analytics feature provides comprehensive data visualization and insights for your GitHub repositories. This document explains how to use the analytics features and interpret the data.

## Accessing Analytics

You can access the analytics dashboard for a repository in two ways:

1. From the repository detail page, click on the "Analytics" link in the repository header.
2. Navigate directly to `/repositories/{id}/analytics` where `{id}` is the repository ID.

## Analytics Dashboard Overview

The analytics dashboard is divided into several sections:

### 1. Summary Statistics

At the top of the page, you'll find summary cards showing:

- **Total Commits**: The total number of commits in the repository
- **Contributors**: The number of unique contributors to the repository
- **Milestones**: The total number of milestones for the repository
- **Special Files**: The total number of special configuration files tracked

### 2. Commit Activity

The Commit Activity tab shows:

- **Commit Frequency**: An area chart showing the number of commits over time
- **Commits by Author**: A bar chart showing the distribution of commits across contributors

This visualization helps you understand:
- When the repository is most active
- Who are the most active contributors
- How commit patterns change over time

### 3. Milestone Status

The Milestone Status tab shows:

- A pie chart displaying the distribution of milestones by status (Completed, In Progress, Overdue)

This visualization helps you understand:
- Overall project progress
- Milestone completion rates
- Potential bottlenecks in project timelines

### 4. Special Files

The Special Files tab shows:

- A bar chart displaying the distribution of special configuration files by type (.dockerwrapper, .claude, .cursor)

This visualization helps you understand:
- Which configuration files are most used in the repository
- How configuration files are distributed

## Filtering Data

You can filter the analytics data using:

1. **Date Range Picker**: Select a specific time period to analyze
2. **Data Type Selector**: Choose to view all data or focus on specific types (commits, milestones)

## Exporting Data

The analytics dashboard allows you to export data in two formats:

1. **CSV Export**: Click the "Export CSV" button to download a CSV file containing the raw data
2. **PDF Export**: Click the "Export PDF" button to download a PDF report with visualizations

## API Access

You can also access analytics data programmatically through the API:

```
GET /api/v1/repositories/{id}/analytics/files
GET /api/v1/commits/analytics
GET /api/v1/milestones/analytics
GET /api/v1/repositories/{id}/export
```

## Interpreting the Data

### Commit Patterns

- **Spikes in commit activity** often indicate feature releases or bug fix pushes
- **Regular, consistent commits** suggest an active, well-maintained repository
- **Long periods without commits** may indicate project stagnation or pauses in development

### Author Distribution

- **Even distribution** across multiple authors suggests a collaborative project
- **Dominated by one or two authors** may indicate knowledge silos or maintenance by a small team

### Milestone Completion

- **High completion rate** indicates good project management and realistic planning
- **Many overdue milestones** may suggest scope creep or unrealistic deadlines

## Best Practices

1. **Regular Review**: Check analytics weekly or monthly to track project health
2. **Compare Periods**: Use date filtering to compare different development periods
3. **Share Reports**: Export PDF reports to share with stakeholders
4. **Set Goals**: Use analytics data to set realistic goals for commit frequency and milestone completion

## Troubleshooting

### No Data Appears

If no data appears in the charts:

1. Ensure the repository has been synced recently (click "Sync Repository" on the repository detail page)
2. Check that the repository has commits, milestones, or special files to display
3. Try clearing any active filters

### Export Fails

If data export fails:

1. Ensure you have permission to export data
2. Try exporting a smaller date range
3. Check your browser's download settings 