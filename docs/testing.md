# DashDevil Testing Guide

This document provides comprehensive information about the testing strategy and implementation for DashDevil.

## Overview

DashDevil implements a multi-layered testing approach to ensure code quality, reliability, and maintainability:

- **Unit Tests**: Test individual components and functions in isolation
- **Integration Tests**: Test interactions between different parts of the system
- **Behavior Tests (BDD)**: Test complete user workflows and business logic
- **End-to-End Tests**: Test the entire application from user perspective

## Testing Stack

### Backend Testing
- **pytest**: Main testing framework
- **pytest-asyncio**: For testing async functions
- **pytest-cov**: Code coverage reporting
- **pytest-mock**: Mocking utilities
- **pytest-bdd**: Behavior-driven development
- **httpx**: HTTP client for API testing
- **faker**: Generate fake data for tests
- **factory-boy**: Create test data factories

### Frontend Testing
- **Jest**: JavaScript testing framework
- **React Testing Library**: Component testing utilities
- **@testing-library/user-event**: User interaction simulation
- **Playwright**: End-to-end testing framework

## Test Structure

```
backend/
├── tests/
│   ├── conftest.py              # Pytest configuration and fixtures
│   ├── unit/                    # Unit tests
│   │   ├── test_models.py       # Database model tests
│   │   ├── test_repositories_api.py  # Repository API tests
│   │   ├── test_commits_api.py  # Commits API tests
│   │   └── ...
│   ├── integration/             # Integration tests
│   │   ├── test_repository_workflow.py  # Complete workflows
│   │   └── ...
│   └── bdd/                     # Behavior-driven tests
│       ├── features/            # Gherkin feature files
│       │   └── repository_management.feature
│       └── step_definitions/    # Step implementations
│           └── test_repository_steps.py

frontend/
├── src/__tests__/
│   ├── setup.ts                 # Test environment setup
│   ├── unit/                    # Unit tests
│   │   ├── components/          # Component tests
│   │   │   ├── AppLayout.test.tsx
│   │   │   ├── Dashboard.test.tsx
│   │   │   └── ...
│   │   ├── repositoryService.test.ts
│   │   ├── commitService.test.ts
│   │   └── ...
│   ├── integration/             # Integration tests
│   │   └── repository-workflow.test.tsx
│   └── e2e/                     # End-to-end tests
│       └── repository-management.spec.ts
```

## Running Tests

### Quick Start
```bash
# Run all tests
./scripts/run-tests.sh

# Run specific test types
./scripts/run-tests.sh backend
./scripts/run-tests.sh frontend
./scripts/run-tests.sh e2e
./scripts/run-tests.sh unit
./scripts/run-tests.sh integration
./scripts/run-tests.sh bdd
```

### Backend Tests
```bash
cd backend

# Run all backend tests
pytest

# Run unit tests only
pytest tests/unit/ -v

# Run integration tests only
pytest tests/integration/ -v

# Run BDD tests only
pytest tests/bdd/ -v

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/unit/test_models.py -v

# Run specific test function
pytest tests/unit/test_models.py::TestUserModel::test_create_user -v
```

### Frontend Tests
```bash
cd frontend

# Run all frontend tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage
npm run test:coverage

# Run unit tests only
npm test -- --testPathPattern="unit"

# Run integration tests only
npm test -- --testPathPattern="integration"

# Run specific test file
npm test -- AppLayout.test.tsx

# Type checking
npm run typecheck

# Linting
npm run lint
```

### End-to-End Tests
```bash
cd frontend

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run E2E tests in headed mode
npm run test:e2e:headed

# Run specific E2E test
npx playwright test repository-management.spec.ts
```

## Test Coverage

### Coverage Targets
- **Backend**: Minimum 80% code coverage
- **Frontend**: Minimum 70% code coverage

### Viewing Coverage Reports
```bash
# Generate and view coverage reports
./scripts/run-tests.sh coverage

# Backend coverage report
open backend/htmlcov/combined/index.html

# Frontend coverage report
open frontend/coverage/lcov-report/index.html
```

## Writing Tests

### Backend Unit Tests
```python
import pytest
from sqlalchemy.orm import Session
from app.models.repository import Repository

@pytest.mark.unit
class TestRepositoryModel:
    def test_create_repository(self, db_session: Session):
        """Test creating a new repository."""
        repo = Repository(
            name="test-repo",
            full_name="user/test-repo",
            description="Test repository"
        )
        db_session.add(repo)
        db_session.commit()
        
        assert repo.id is not None
        assert repo.name == "test-repo"
```

### Backend API Tests
```python
import pytest
from fastapi.testclient import TestClient

@pytest.mark.unit
def test_get_repositories(client: TestClient, test_repository):
    """Test getting all repositories."""
    response = client.get("/api/v1/repositories")
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["name"] == test_repository.name
```

### Frontend Component Tests
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import Dashboard from '../Dashboard';

test('renders dashboard with repository list', async () => {
  render(<Dashboard />);
  
  await waitFor(() => {
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Total Repositories')).toBeInTheDocument();
  });
});
```

### BDD Tests
```gherkin
Feature: Repository Management
  Scenario: Add a new repository
    Given I have a GitHub repository "user/test-repo"
    When I add the repository to DashDevil
    Then the repository should be tracked
    And the repository should appear in my repository list
```

### E2E Tests
```typescript
import { test, expect } from '@playwright/test';

test('should add a new repository', async ({ page }) => {
  await page.goto('/');
  await page.click('text=Add Repository');
  await page.fill('[placeholder*="repository name"]', 'user/test-repo');
  await page.click('button:has-text("Add Repository")');
  
  await expect(page.locator('text=Repository added successfully')).toBeVisible();
});
```

## Test Data Management

### Backend Fixtures
```python
@pytest.fixture
def test_repository(db_session: Session) -> Repository:
    """Create a test repository."""
    repository = Repository(
        name="test-repo",
        full_name="testuser/test-repo",
        description="A test repository"
    )
    db_session.add(repository)
    db_session.commit()
    return repository
```

### Frontend Mocks
```typescript
jest.mock('../../services/api', () => ({
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  }
}));
```

## Continuous Integration

### GitHub Actions
The project includes GitHub Actions workflows for:
- Running tests on pull requests
- Generating coverage reports
- Running E2E tests in different browsers
- Performance testing

### Pre-commit Hooks
```bash
# Install pre-commit hooks
pip install pre-commit
pre-commit install

# Run hooks manually
pre-commit run --all-files
```

## Best Practices

### General
1. **Test Isolation**: Each test should be independent
2. **Descriptive Names**: Use clear, descriptive test names
3. **Arrange-Act-Assert**: Structure tests clearly
4. **Mock External Dependencies**: Avoid real API calls in tests
5. **Test Edge Cases**: Include error handling and boundary conditions

### Backend
1. Use database transactions for test isolation
2. Mock GitHub API calls
3. Test both success and error scenarios
4. Use factories for creating test data
5. Test database constraints and relationships

### Frontend
1. Test user interactions, not implementation details
2. Use semantic queries (getByRole, getByLabelText)
3. Test accessibility features
4. Mock API calls consistently
5. Test responsive behavior

### E2E
1. Test critical user journeys
2. Use page object patterns for complex flows
3. Test across different browsers and devices
4. Keep tests stable and reliable
5. Use proper wait strategies

## Debugging Tests

### Backend
```bash
# Run with verbose output
pytest -v -s

# Run with debugger
pytest --pdb

# Run with coverage and show missing lines
pytest --cov=app --cov-report=term-missing
```

### Frontend
```bash
# Run with verbose output
npm test -- --verbose

# Debug specific test
npm test -- --testNamePattern="specific test name"

# Run in watch mode for debugging
npm run test:watch
```

### E2E
```bash
# Run with headed browser
npm run test:e2e:headed

# Run with UI mode
npm run test:e2e:ui

# Debug specific test
npx playwright test --debug repository-management.spec.ts
```

## Maintenance

### Updating Test Dependencies
```bash
# Backend
pip install -r requirements.txt --upgrade

# Frontend
npm update
```

### Cleaning Test Artifacts
```bash
# Clean all test artifacts
./scripts/run-tests.sh clean
```

### Performance Monitoring
- Monitor test execution time
- Identify slow tests and optimize
- Use parallel execution where possible
- Regular dependency updates
