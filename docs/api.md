# API Documentation

RepoTimeline provides a comprehensive RESTful API that allows you to programmatically interact with all features of the platform.

## Authentication

All API requests require authentication using an API key or OAuth token.

### Obtaining an API Key

1. Log in to your RepoTimeline account
2. Navigate to your profile page
3. Select the "API Keys" tab
4. Click "Generate New API Key"
5. Store the key securely - it will only be shown once

### Authentication Methods

#### API Key Authentication

Include your API key in the request header:

```
Authorization: Bearer YOUR_API_KEY
```

#### OAuth Authentication

For third-party applications, use the OAuth 2.0 flow:

1. Redirect users to `/api/v1/auth/oauth/authorize`
2. Receive the authorization code
3. Exchange the code for a token at `/api/v1/auth/oauth/token`
4. Use the token in the Authorization header

## API Endpoints

### Repositories

#### List Repositories

```
GET /api/v1/repositories
```

Query parameters:
- `include_deleted` (boolean): Include soft-deleted repositories
- `skip` (integer): Number of items to skip for pagination
- `limit` (integer): Maximum number of items to return

Response:
```json
[
  {
    "id": 1,
    "name": "example-repo",
    "full_name": "owner/example-repo",
    "description": "An example repository",
    "url": "https://api.github.com/repos/owner/example-repo",
    "html_url": "https://github.com/owner/example-repo",
    "clone_url": "https://github.com/owner/example-repo.git",
    "default_branch": "main",
    "stars": 42,
    "forks": 10,
    "open_issues": 5,
    "is_active": true,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-06-01T00:00:00Z",
    "last_synced_at": "2023-06-15T00:00:00Z"
  }
]
```

#### Get Repository

```
GET /api/v1/repositories/{id}
```

Query parameters:
- `include_deleted` (boolean): Include if soft-deleted

Response:
```json
{
  "id": 1,
  "name": "example-repo",
  "full_name": "owner/example-repo",
  "description": "An example repository",
  "url": "https://api.github.com/repos/owner/example-repo",
  "html_url": "https://github.com/owner/example-repo",
  "clone_url": "https://github.com/owner/example-repo.git",
  "default_branch": "main",
  "stars": 42,
  "forks": 10,
  "open_issues": 5,
  "is_active": true,
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-06-01T00:00:00Z",
  "last_synced_at": "2023-06-15T00:00:00Z",
  "special_files": [...],
  "commits": [...],
  "milestones": [...]
}
```

#### Create Repository

```
POST /api/v1/repositories
```

Request body:
```json
{
  "name": "example-repo",
  "full_name": "owner/example-repo",
  "description": "An example repository",
  "url": "https://api.github.com/repos/owner/example-repo",
  "html_url": "https://github.com/owner/example-repo",
  "clone_url": "https://github.com/owner/example-repo.git",
  "default_branch": "main"
}
```

#### Update Repository

```
PUT /api/v1/repositories/{id}
```

Request body:
```json
{
  "description": "Updated description",
  "is_active": true
}
```

#### Delete Repository

```
DELETE /api/v1/repositories/{id}
```

Query parameters:
- `permanent` (boolean): Permanently delete instead of soft delete

#### Restore Repository

```
POST /api/v1/repositories/{id}/restore
```

#### Sync Repository

```
POST /api/v1/repositories/{id}/sync
```

### Special Files

#### List Special Files

```
GET /api/v1/special-files
```

Query parameters:
- `repository_id` (integer): Filter by repository
- `file_type` (string): Filter by file type
- `include_deleted` (boolean): Include soft-deleted files
- `skip` (integer): Number of items to skip for pagination
- `limit` (integer): Maximum number of items to return

#### Get Special File

```
GET /api/v1/special-files/{id}
```

#### Create Special File

```
POST /api/v1/special-files
```

Request body:
```json
{
  "repository_id": 1,
  "file_type": "dockerwrapper",
  "file_path": ".dockerwrapper",
  "content": "# Docker wrapper configuration"
}
```

#### Update Special File

```
PUT /api/v1/special-files/{id}
```

Request body:
```json
{
  "file_path": ".dockerwrapper",
  "content": "# Updated Docker wrapper configuration"
}
```

#### Delete Special File

```
DELETE /api/v1/special-files/{id}
```

Query parameters:
- `permanent` (boolean): Permanently delete instead of soft delete

#### Restore Special File

```
POST /api/v1/special-files/{id}/restore
```

#### Sync Special Files

```
POST /api/v1/special-files/sync/{repository_id}
```

### Commits

#### List Commits

```
GET /api/v1/commits
```

Query parameters:
- `repository_id` (integer): Filter by repository
- `author` (string): Filter by author
- `since` (string): Filter by date (ISO format)
- `until` (string): Filter by date (ISO format)
- `include_deleted` (boolean): Include soft-deleted commits
- `skip` (integer): Number of items to skip for pagination
- `limit` (integer): Maximum number of items to return

#### Get Commit

```
GET /api/v1/commits/{id}
```

#### Create Commit

```
POST /api/v1/commits
```

Request body:
```json
{
  "repository_id": 1,
  "sha": "abc123",
  "message": "Fix bug",
  "author": "John Doe",
  "author_email": "<EMAIL>",
  "committed_date": "2023-06-15T12:00:00Z"
}
```

#### Update Commit

```
PUT /api/v1/commits/{id}
```

#### Delete Commit

```
DELETE /api/v1/commits/{id}
```

Query parameters:
- `permanent` (boolean): Permanently delete instead of soft delete

#### Restore Commit

```
POST /api/v1/commits/{id}/restore
```

#### Sync Commits

```
POST /api/v1/commits/sync/{repository_id}
```

Query parameters:
- `limit` (integer): Maximum number of commits to sync

### Milestones

#### List Milestones

```
GET /api/v1/milestones
```

Query parameters:
- `repository_id` (integer): Filter by repository
- `is_completed` (boolean): Filter by completion status
- `due_before` (string): Filter by due date (ISO format)
- `due_after` (string): Filter by due date (ISO format)
- `include_deleted` (boolean): Include soft-deleted milestones
- `skip` (integer): Number of items to skip for pagination
- `limit` (integer): Maximum number of items to return

#### Get Milestone

```
GET /api/v1/milestones/{id}
```

#### Create Milestone

```
POST /api/v1/milestones
```

Request body:
```json
{
  "repository_id": 1,
  "title": "Version 1.0",
  "description": "First stable release",
  "due_date": "2023-12-31T00:00:00Z",
  "is_completed": false
}
```

#### Update Milestone

```
PUT /api/v1/milestones/{id}
```

Request body:
```json
{
  "title": "Updated title",
  "description": "Updated description",
  "due_date": "2024-01-31T00:00:00Z",
  "is_completed": true
}
```

#### Delete Milestone

```
DELETE /api/v1/milestones/{id}
```

Query parameters:
- `permanent` (boolean): Permanently delete instead of soft delete

#### Restore Milestone

```
POST /api/v1/milestones/{id}/restore
```

#### Complete Milestone

```
POST /api/v1/milestones/{id}/complete
```

#### Reopen Milestone

```
POST /api/v1/milestones/{id}/reopen
```

### Analytics

#### Get File Type Analytics

```
GET /api/v1/repositories/{id}/analytics/files
```

Response:
```json
[
  {
    "type": "dockerwrapper",
    "count": 5
  },
  {
    "type": "claude",
    "count": 3
  },
  {
    "type": "cursor",
    "count": 7
  }
]
```

#### Get Commit Analytics

```
GET /api/v1/commits/analytics
```

Query parameters:
- `repository_id` (integer): Repository ID
- `start_date` (string): Start date (ISO format)
- `end_date` (string): End date (ISO format)
- `author` (string): Filter by author

Response:
```json
{
  "byDate": [
    {
      "date": "2023-06-01",
      "count": 5
    },
    {
      "date": "2023-06-02",
      "count": 3
    }
  ],
  "byAuthor": [
    {
      "author": "John Doe",
      "commits": 12
    },
    {
      "author": "Jane Smith",
      "commits": 8
    }
  ]
}
```

#### Get Milestone Analytics

```
GET /api/v1/milestones/analytics
```

Query parameters:
- `repository_id` (integer): Repository ID

Response:
```json
{
  "byStatus": [
    {
      "status": "Completed",
      "count": 5
    },
    {
      "status": "In Progress",
      "count": 3
    },
    {
      "status": "Overdue",
      "count": 2
    }
  ]
}
```

#### Export Repository Data

```
GET /api/v1/repositories/{id}/export
```

Query parameters:
- `format` (string): Export format ('csv' or 'pdf')

Response:
- For CSV: A downloadable CSV file
- For PDF: A downloadable PDF file

## Rate Limiting

API requests are subject to rate limiting:

- 60 requests per minute for authenticated users
- 5,000 requests per day per API key

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1623238800
```

## Error Handling

The API uses standard HTTP status codes and returns error details in the response body:

```json
{
  "error": {
    "code": "not_found",
    "message": "Repository not found",
    "details": "No repository with ID 123 exists"
  }
}
```

Common error codes:
- `400 Bad Request`: Invalid input
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## Pagination

List endpoints support pagination using `skip` and `limit` parameters:

```
GET /api/v1/repositories?skip=0&limit=10
```

Response includes pagination metadata:

```json
{
  "data": [...],
  "pagination": {
    "total": 42,
    "skip": 0,
    "limit": 10
  }
}
```

## Versioning

The API is versioned in the URL path (`/api/v1/`). When breaking changes are introduced, a new version will be created. 