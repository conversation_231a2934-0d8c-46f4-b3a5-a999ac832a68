#!/bin/bash

# RepoTimeline Docker Infrastructure Configuration

# Project configuration
export PROJECT_NAME=repotimeline
export COMPOSE_PROJECT_NAME=repotimeline

# Environment configuration
export ENV=development

# Docker image versions
export POSTGRES_VERSION=15
export PYTHON_VERSION=3.11
export NODE_VERSION=18

# Service ports
export BACKEND_PORT=8000
export FRONTEND_PORT=3000
export POSTGRES_PORT=5432

# Database configuration
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=postgres
export POSTGRES_DB=repotimeline

# Volume paths
export POSTGRES_DATA_PATH=./data/postgres

# Docker Compose files
export COMPOSE_FILE=docker/docker-compose.yml
export COMPOSE_FILE_PROD=docker/docker-compose.prod.yml

# Docker build context
export BUILD_CONTEXT=.

# Docker network
export NETWORK_NAME=repotimeline-network

# Health check configuration
export HEALTH_CHECK_INTERVAL=30s
export HEALTH_CHECK_TIMEOUT=10s
export HEALTH_CHECK_RETRIES=5

# Logging configuration
export LOG_LEVEL=info
export LOG_DRIVER=json-file
export LOG_MAX_SIZE=10m
export LOG_MAX_FILE=3

# Resource limits
export MEMORY_LIMIT_BACKEND=512M
export MEMORY_LIMIT_FRONTEND=256M
export MEMORY_LIMIT_POSTGRES=1G
export CPU_LIMIT_BACKEND=0.5
export CPU_LIMIT_FRONTEND=0.25
export CPU_LIMIT_POSTGRES=1.0

# Backup configuration
export BACKUP_PATH=./backups
export BACKUP_RETENTION_DAYS=7

# CI/CD configuration
export CI_REGISTRY=docker.io
export CI_REGISTRY_USER=${CI_REGISTRY_USER:-}
export CI_REGISTRY_PASSWORD=${CI_REGISTRY_PASSWORD:-}
export CI_REGISTRY_IMAGE=${CI_REGISTRY_IMAGE:-} 