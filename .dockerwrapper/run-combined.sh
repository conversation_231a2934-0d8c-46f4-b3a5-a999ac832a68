#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Check if <PERSON><PERSON> is running
if ! docker info &> /dev/null; then
    echo "Error: Docker is not running. Please start Docker first."
    exit 1
fi

# Build the combined container
echo "Building the combined container..."
docker build -t certrats-combined -f .dockerwrapper/Dockerfile.combined .

# Run the combined container
echo "Running the combined container..."
docker run -p 80:80 --name certrats-app certrats-combined 