# Multi-stage build for optimized production image

# Frontend build stage
FROM node:18-alpine AS frontend-build
WORKDIR /app
COPY frontend/package*.json ./
RUN npm ci
COPY frontend/ ./
RUN npm run build

# Final stage
FROM python:3.11-slim
WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    nginx \
    postgresql \
    postgresql-contrib \
    supervisor \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy backend files
COPY api/ /app/api/
COPY models/ /app/models/
COPY utils/ /app/utils/
COPY database.py /app/
COPY alembic.ini /app/
COPY migrations/ /app/migrations/

# Copy Streamlit files (during transition)
COPY main.py /app/
COPY pages/ /app/pages/
COPY components/ /app/components/
COPY .streamlit/ /app/.streamlit/

# Copy frontend build from build stage
COPY --from=frontend-build /app/build /app/frontend/build

# Copy configuration files
COPY .dockerwrapper/nginx.conf /etc/nginx/sites-available/default
COPY .dockerwrapper/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose the port
EXPOSE 80

# Start supervisord
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"] 