#!/bin/bash

# RepoTimeline Docker Infrastructure Management Script

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/config.sh"

# Commands
start() {
  echo "Starting RepoTimeline services..."
  docker-compose -f ${COMPOSE_FILE} up -d
  echo "Services started successfully."
}

stop() {
  echo "Stopping RepoTimeline services..."
  docker-compose -f ${COMPOSE_FILE} down
  echo "Services stopped successfully."
}

restart() {
  echo "Restarting RepoTimeline services..."
  docker-compose -f ${COMPOSE_FILE} restart
  echo "Services restarted successfully."
}

logs() {
  docker-compose -f ${COMPOSE_FILE} logs -f "$@"
}

status() {
  docker-compose -f ${COMPOSE_FILE} ps
}

build() {
  echo "Building RepoTimeline services..."
  docker-compose -f ${COMPOSE_FILE} build "$@"
  echo "Services built successfully."
}

shell() {
  local service=$1
  shift
  docker-compose -f ${COMPOSE_FILE} exec ${service} bash "$@"
}

backup() {
  echo "Creating database backup..."
  mkdir -p ${BACKUP_PATH}
  BACKUP_FILE="${BACKUP_PATH}/backup-$(date +%Y%m%d-%H%M%S).sql"
  docker-compose -f ${COMPOSE_FILE} exec -T db pg_dump -U ${POSTGRES_USER} ${POSTGRES_DB} > ${BACKUP_FILE}
  echo "Backup created: ${BACKUP_FILE}"
  
  # Clean up old backups
  find ${BACKUP_PATH} -name "backup-*.sql" -type f -mtime +${BACKUP_RETENTION_DAYS} -delete
}

restore() {
  local backup_file=$1
  if [ -z "${backup_file}" ]; then
    echo "Error: Backup file not specified."
    echo "Usage: ./.dockerwrapper/run.sh restore <backup_file>"
    return 1
  fi
  
  if [ ! -f "${backup_file}" ]; then
    echo "Error: Backup file '${backup_file}' not found."
    return 1
  fi
  
  echo "Restoring database from backup: ${backup_file}"
  docker-compose -f ${COMPOSE_FILE} exec -T db psql -U ${POSTGRES_USER} ${POSTGRES_DB} < ${backup_file}
  echo "Database restored successfully."
}

migrate() {
  echo "Running database migrations..."
  docker-compose -f ${COMPOSE_FILE} exec backend alembic upgrade head
  echo "Migrations completed successfully."
}

test() {
  echo "Running tests..."
  docker-compose -f ${COMPOSE_FILE} exec backend pytest "$@"
  echo "Tests completed."
}

lint() {
  echo "Running linters..."
  docker-compose -f ${COMPOSE_FILE} exec backend flake8 app tests
  docker-compose -f ${COMPOSE_FILE} exec backend black --check app tests
  docker-compose -f ${COMPOSE_FILE} exec backend isort --check-only app tests
  echo "Linting completed."
}

format() {
  echo "Formatting code..."
  docker-compose -f ${COMPOSE_FILE} exec backend black app tests
  docker-compose -f ${COMPOSE_FILE} exec backend isort app tests
  echo "Formatting completed."
}

deploy() {
  echo "Deploying to production..."
  docker-compose -f ${COMPOSE_FILE} -f ${COMPOSE_FILE_PROD} up -d
  echo "Deployment completed successfully."
}

help() {
  echo "RepoTimeline Docker Infrastructure Management"
  echo ""
  echo "Usage: ./.dockerwrapper/run.sh <command> [options]"
  echo ""
  echo "Commands:"
  echo "  start             Start all services"
  echo "  stop              Stop all services"
  echo "  restart           Restart all services"
  echo "  logs [service]    View logs for all or specific service"
  echo "  status            Show status of services"
  echo "  build [service]   Build all or specific service"
  echo "  shell <service>   Open a shell in a service container"
  echo "  backup            Create a database backup"
  echo "  restore <file>    Restore database from backup file"
  echo "  migrate           Run database migrations"
  echo "  test [args]       Run tests with optional arguments"
  echo "  lint              Run linters"
  echo "  format            Format code"
  echo "  deploy            Deploy to production"
  echo "  help              Show this help message"
}

# Main command handler
if [ $# -eq 0 ]; then
  help
  exit 0
fi

command=$1
shift

case ${command} in
  start|stop|restart|logs|status|build|shell|backup|restore|migrate|test|lint|format|deploy|help)
    ${command} "$@"
    ;;
  *)
    echo "Error: Unknown command '${command}'"
    help
    exit 1
    ;;
esac 