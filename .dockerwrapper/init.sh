#!/bin/bash
set -e

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to start..."
until pg_isready -h localhost -U postgres; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 1
done

# Create database if it doesn't exist
echo "Creating database if it doesn't exist..."
psql -U postgres -tc "SELECT 1 FROM pg_database WHERE datname = 'certrats'" | grep -q 1 || psql -U postgres -c "CREATE DATABASE certrats"

# Run database migrations
echo "Running database migrations..."
cd /app
alembic upgrade head

echo "Initialization complete!"
exit 0 