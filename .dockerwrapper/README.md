# Docker Setup for CertRats

This directory contains all Docker-related files for the CertRats application.

## Available Scripts

- `run.sh`: Run the multi-container setup using Docker Compose
- `run-combined.sh`: Build and run the single-container setup
- `update-routes.sh`: Update Nginx routes for progressive migration
- `migrate-feature.sh`: Helper script for feature migration
- `remove-streamlit.sh`: Helper script for final Streamlit removal

## Multi-Container Setup

The multi-container setup uses Docker Compose to run the following services:

- **nginx**: Reverse proxy that routes traffic to the appropriate services
- **fastapi**: Backend API service
- **streamlit**: Legacy frontend service (to be progressively replaced)
- **react**: New frontend service (will progressively replace Streamlit)
- **db**: PostgreSQL database service

### Usage

```bash
# Start all services
./.dockerwrapper/run.sh up

# Start all services in detached mode
./.dockerwrapper/run.sh up -d

# Stop all services
./.dockerwrapper/run.sh down

# View logs
./.dockerwrapper/run.sh logs -f
```

## Single-Container Setup

The single-container setup combines all services into a single Docker container using Supervisor to manage the processes.

### Usage

```bash
# Build and run the combined container
./.dockerwrapper/run-combined.sh
```

## Progressive Migration

The Docker setup is designed to support the progressive migration from Streamlit to React:

1. Initially, most traffic is routed to Streamlit
2. As features are migrated to React, the Nginx configuration is updated to route specific paths to React
3. Eventually, all traffic will be routed to React and Streamlit will be removed

### Managing Routes

The `update-routes.sh` script helps manage the routing between Streamlit and React:

```bash
# Add a new route to be handled by React
./.dockerwrapper/update-routes.sh --add-react-route /dashboard

# Remove a route from React handling (revert to Streamlit)
./.dockerwrapper/update-routes.sh --remove-react-route /dashboard

# List current route mappings
./.dockerwrapper/update-routes.sh --list-routes
```

After updating routes, you need to restart the services for changes to take effect:

```bash
./.dockerwrapper/run.sh restart nginx
```

### Feature Migration

The `migrate-feature.sh` script helps automate the feature migration process:

```bash
# Start API development for a feature
./.dockerwrapper/migrate-feature.sh --start-api dashboard

# Show migration status
./.dockerwrapper/migrate-feature.sh --status
```

See the [Migration Guide](../MIGRATION.md) for more information.

### Streamlit Removal

Once all features have been migrated to React, you can use the `remove-streamlit.sh` script to remove Streamlit:

```bash
# Check if all features have been migrated
./.dockerwrapper/remove-streamlit.sh --check

# Remove Streamlit and update configuration
./.dockerwrapper/remove-streamlit.sh --remove
```

## Directory Structure

- `docker-compose.yml`: Configuration for the multi-container setup
- `Dockerfile.api`: Dockerfile for the FastAPI service
- `Dockerfile.streamlit`: Dockerfile for the Streamlit service
- `Dockerfile.react`: Dockerfile for the React service
- `Dockerfile.combined`: Dockerfile for the single-container setup
- `nginx.conf`: Nginx configuration for routing traffic
- `supervisord.conf`: Supervisor configuration for the single-container setup
- `init.sh`: Initialization script for the single-container setup
- `run.sh`: Script to run the multi-container setup
- `run-combined.sh`: Script to run the single-container setup
- `update-routes.sh`: Script to update Nginx routes for progressive migration
- `migrate-feature.sh`: Script to help with feature migration
- `remove-streamlit.sh`: Script to help with final Streamlit removal 