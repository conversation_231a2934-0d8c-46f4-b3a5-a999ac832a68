version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: dashdevil_nginx
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ../frontend/build:/app/frontend/build
    depends_on:
      - fastapi
      - streamlit
    networks:
      - dashdevil-network

  fastapi:
    build:
      context: ../backend
      dockerfile: ../.dockerwrapper/Dockerfile.api
    container_name: dashdevil_fastapi
    volumes:
      - ../backend:/app
    environment:
      - DATABASE_URL=*******************************************
      - SECRET_KEY=development_secret_key
      - ENVIRONMENT=development
    depends_on:
      - db
    networks:
      - dashdevil-network

  streamlit:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.streamlit
    container_name: dashdevil_streamlit
    volumes:
      - ../:/app
    environment:
      - DATABASE_URL=*******************************************
    ports:
      - "8501:8501"
    depends_on:
      - db
      - fastapi
    networks:
      - dashdevil-network

  react:
    build:
      context: ../frontend
      dockerfile: ../.dockerwrapper/Dockerfile.react
    container_name: dashdevil_react
    volumes:
      - ../frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8080/api
      - NODE_ENV=development
    ports:
      - "3001:3000"
    networks:
      - dashdevil-network

  db:
    image: postgres:14
    container_name: dashdevil_db
    volumes:
      - dashdevil_postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=dashdevil
    ports:
      - "5433:5432"
    networks:
      - dashdevil-network

networks:
  dashdevil-network:
    name: dashdevil-network
    driver: bridge

volumes:
  dashdevil_postgres_data: 