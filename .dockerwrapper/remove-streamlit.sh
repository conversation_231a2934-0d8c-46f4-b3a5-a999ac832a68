#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --check       Check if all features have been migrated"
    echo "  --remove      Remove Streamlit and update configuration"
    echo "  --help        Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --check"
    echo "  $0 --remove"
}

# Function to check if all features have been migrated
function check_migration {
    echo "Checking migration status..."
    
    # Get the number of features with UI status not marked as Complete
    incomplete_features=$(grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "|" | grep -v "Feature" | grep -v "Complete.*|.*Complete.*|.*Complete" | wc -l)
    
    if [ "$incomplete_features" -gt 0 ]; then
        echo "Error: Not all features have been migrated to React."
        echo "Please complete the migration of the following features:"
        grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "|" | grep -v "Feature" | grep -v "Complete.*|.*Complete.*|.*Complete"
        exit 1
    else
        echo "All features have been migrated to React. You can safely remove Streamlit."
    fi
}

# Function to remove Streamlit
function remove_streamlit {
    echo "Removing Streamlit..."
    
    # Check if all features have been migrated
    check_migration
    
    # Update docker-compose.yml to remove Streamlit service
    echo "Updating docker-compose.yml..."
    sed -i '/streamlit:/,/networks:/d' .dockerwrapper/docker-compose.yml
    sed -i 's/- streamlit//g' .dockerwrapper/docker-compose.yml
    
    # Update Nginx configuration to route all traffic to React
    echo "Updating Nginx configuration..."
    cp .dockerwrapper/nginx.conf .dockerwrapper/nginx.conf.bak
    cat > .dockerwrapper/nginx.conf << EOF
server {
    listen 80;
    server_name localhost;

    # API endpoints
    location /api/ {
        proxy_pass http://fastapi:8000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # React UI (all routes)
    location / {
        proxy_pass http://react:3000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
EOF
    
    # Update Dockerfile.combined to remove Streamlit
    echo "Updating Dockerfile.combined..."
    sed -i '/# Copy Streamlit files/,/# Copy frontend build/d' .dockerwrapper/Dockerfile.combined
    sed -i '/streamlit/d' .dockerwrapper/supervisord.conf
    
    # Update README.md to remove Streamlit references
    echo "Updating README.md..."
    sed -i 's/- Streamlit frontend (legacy)//' README.md
    sed -i 's/- Streamlit UI: http:\/\/localhost\///' README.md
    
    # Update MIGRATION.md to mark Streamlit removal as complete
    echo "Updating MIGRATION.md..."
    sed -i '/### Phase 5: Streamlit Removal/,/4. Update documentation/c\### Phase 5: Streamlit Removal\n\nStreamlit has been successfully removed from the application:\n\n1. ✓ Nginx configuration updated to route all traffic to React\n2. ✓ Streamlit dependencies removed\n3. ✓ Streamlit-specific code cleaned up\n4. ✓ Documentation updated' MIGRATION.md
    
    echo "Streamlit has been successfully removed from the application."
    echo "You need to restart the services for changes to take effect:"
    echo "./.dockerwrapper/run.sh down"
    echo "./.dockerwrapper/run.sh up -d"
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

while [ $# -gt 0 ]; do
    case "$1" in
        --check)
            check_migration
            shift
            ;;
        --remove)
            remove_streamlit
            shift
            ;;
        --help)
            display_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
    esac
done 