#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo "Error: jq is not installed. Please install it first."
    exit 1
fi

# Function to display usage
function display_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --add-react-route PATH    Add a new route to be handled by React"
    echo "  --remove-react-route PATH Remove a route from React handling"
    echo "  --list-routes             List current route mappings"
    echo "  --help                    Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --add-react-route /dashboard"
    echo "  $0 --remove-react-route /dashboard"
    echo "  $0 --list-routes"
}

# Function to list current routes
function list_routes {
    echo "Current route mappings:"
    echo "----------------------"
    echo "React routes:"
    grep -A 1 "location /new/" .dockerwrapper/nginx.conf | grep -v "location" | grep -v "\-\-" | sed 's/proxy_pass.*//'
    echo ""
    echo "Streamlit routes:"
    grep -A 1 "location /" .dockerwrapper/nginx.conf | grep -v "location /new" | grep -v "location /api" | grep -v "\-\-" | sed 's/proxy_pass.*//'
}

# Function to add a React route
function add_react_route {
    local path=$1
    
    # Create a backup of the current nginx.conf
    cp .dockerwrapper/nginx.conf .dockerwrapper/nginx.conf.bak
    
    # Add the new location block before the Streamlit catch-all location
    sed -i "/# Streamlit (legacy UI)/i \
    # React UI for $path\n\
    location $path {\n\
        proxy_pass http://react:3000$path;\n\
        proxy_set_header Host \$host;\n\
        proxy_set_header X-Real-IP \$remote_addr;\n\
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;\n\
        proxy_set_header X-Forwarded-Proto \$scheme;\n\
        proxy_set_header Upgrade \$http_upgrade;\n\
        proxy_set_header Connection \"upgrade\";\n\
    }\n" .dockerwrapper/nginx.conf
    
    echo "Added React route for $path"
    echo "Nginx configuration updated. You need to restart the services for changes to take effect."
}

# Function to remove a React route
function remove_react_route {
    local path=$1
    
    # Create a backup of the current nginx.conf
    cp .dockerwrapper/nginx.conf .dockerwrapper/nginx.conf.bak
    
    # Remove the location block for the specified path
    sed -i "/# React UI for $path/,/}/d" .dockerwrapper/nginx.conf
    
    echo "Removed React route for $path"
    echo "Nginx configuration updated. You need to restart the services for changes to take effect."
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

while [ $# -gt 0 ]; do
    case "$1" in
        --add-react-route)
            if [ -z "$2" ]; then
                echo "Error: No path specified for --add-react-route"
                exit 1
            fi
            add_react_route "$2"
            shift 2
            ;;
        --remove-react-route)
            if [ -z "$2" ]; then
                echo "Error: No path specified for --remove-react-route"
                exit 1
            fi
            remove_react_route "$2"
            shift 2
            ;;
        --list-routes)
            list_routes
            shift
            ;;
        --help)
            display_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
    esac
done 