#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options] FEATURE_NAME"
    echo "Options:"
    echo "  --start-api      Start API development for the feature"
    echo "  --start-test     Start test development for the feature"
    echo "  --start-ui       Start UI development for the feature"
    echo "  --complete-api   Mark API development as complete"
    echo "  --complete-test  Mark test development as complete"
    echo "  --complete-ui    Mark UI development as complete"
    echo "  --status         Show migration status"
    echo "  --help           Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --start-api dashboard"
    echo "  $0 --complete-api dashboard"
    echo "  $0 --status"
}

# Function to update migration status
function update_status {
    local feature=$1
    local phase=$2
    local status=$3
    
    # Convert feature name to title case
    feature_title=$(echo $feature | sed 's/\b\(.\)/\u\1/g')
    
    # Check if feature exists in migration tracking table
    if ! grep -q "| $feature_title " MIGRATION.md; then
        # Add new feature to the table
        sed -i "/| Admin Interface .*/a | $feature_title | Not Started | Not Started | Not Started | - |" MIGRATION.md
    fi
    
    # Update status for the specified phase
    case $phase in
        api)
            sed -i "s/| $feature_title | [^|]* | /| $feature_title | $status | /" MIGRATION.md
            ;;
        test)
            sed -i "s/| $feature_title | [^|]* | [^|]* | /| $feature_title | [^|]* | $status | /" MIGRATION.md
            ;;
        ui)
            sed -i "s/| $feature_title | [^|]* | [^|]* | [^|]* | /| $feature_title | [^|]* | [^|]* | $status | /" MIGRATION.md
            ;;
    esac
    
    # If UI is complete, update migration date
    if [ "$phase" == "ui" ] && [ "$status" == "Complete" ]; then
        current_date=$(date +"%Y-%m-%d")
        sed -i "s/| $feature_title | [^|]* | [^|]* | [^|]* | [^|]* |/| $feature_title | [^|]* | [^|]* | [^|]* | $current_date |/" MIGRATION.md
    fi
    
    echo "Updated migration status for $feature_title $phase to $status"
}

# Function to show migration status
function show_status {
    echo "Migration Status:"
    echo "----------------"
    grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "|"
}

# Function to create API files
function create_api_files {
    local feature=$1
    
    # Create API endpoint file
    mkdir -p api/endpoints
    
    if [ ! -f "api/endpoints/${feature}.py" ]; then
        cat > "api/endpoints/${feature}.py" << EOF
"""
API endpoints for ${feature} feature
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from models.${feature} import ${feature^}Model, ${feature^}Create, ${feature^}Update
from database import get_db
from sqlalchemy.orm import Session

router = APIRouter(
    prefix="/${feature}",
    tags=["${feature}"],
    responses={404: {"description": "${feature^} not found"}},
)

@router.get("/", response_model=List[${feature^}Model])
async def get_all_${feature}s(
    skip: int = 0, 
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all ${feature}s
    """
    # TODO: Implement ${feature} retrieval logic
    return []

@router.get("/{${feature}_id}", response_model=${feature^}Model)
async def get_${feature}(
    ${feature}_id: int,
    db: Session = Depends(get_db)
):
    """
    Get a specific ${feature} by ID
    """
    # TODO: Implement ${feature} retrieval logic
    return None

@router.post("/", response_model=${feature^}Model)
async def create_${feature}(
    ${feature}: ${feature^}Create,
    db: Session = Depends(get_db)
):
    """
    Create a new ${feature}
    """
    # TODO: Implement ${feature} creation logic
    return None

@router.put("/{${feature}_id}", response_model=${feature^}Model)
async def update_${feature}(
    ${feature}_id: int,
    ${feature}: ${feature^}Update,
    db: Session = Depends(get_db)
):
    """
    Update a ${feature}
    """
    # TODO: Implement ${feature} update logic
    return None

@router.delete("/{${feature}_id}")
async def delete_${feature}(
    ${feature}_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete a ${feature}
    """
    # TODO: Implement ${feature} deletion logic
    return {"message": "${feature^} deleted"}
EOF
        echo "Created API endpoint file: api/endpoints/${feature}.py"
    else
        echo "API endpoint file already exists: api/endpoints/${feature}.py"
    fi
    
    # Create model file
    mkdir -p models
    
    if [ ! -f "models/${feature}.py" ]; then
        cat > "models/${feature}.py" << EOF
"""
Models for ${feature} feature
"""
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class ${feature^}Base(BaseModel):
    """Base ${feature} model"""
    name: str
    description: Optional[str] = None

class ${feature^}Create(${feature^}Base):
    """${feature^} creation model"""
    pass

class ${feature^}Update(${feature^}Base):
    """${feature^} update model"""
    name: Optional[str] = None

class ${feature^}Model(${feature^}Base):
    """${feature^} response model"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        orm_mode = True
EOF
        echo "Created model file: models/${feature}.py"
    else
        echo "Model file already exists: models/${feature}.py"
    fi
}

# Function to create test files
function create_test_files {
    local feature=$1
    
    # Create test file
    mkdir -p tests
    
    if [ ! -f "tests/test_${feature}.py" ]; then
        cat > "tests/test_${feature}.py" << EOF
"""
Tests for ${feature} feature
"""
import pytest
from fastapi.testclient import TestClient
from api.app import app

client = TestClient(app)

def test_get_all_${feature}s():
    """Test getting all ${feature}s"""
    response = client.get("/${feature}/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_get_${feature}():
    """Test getting a specific ${feature}"""
    # TODO: Create a test ${feature} first
    ${feature}_id = 1
    response = client.get(f"/${feature}/{${feature}_id}")
    assert response.status_code == 200
    # TODO: Add more assertions

def test_create_${feature}():
    """Test creating a ${feature}"""
    ${feature}_data = {
        "name": "Test ${feature^}",
        "description": "Test description"
    }
    response = client.post("/${feature}/", json=${feature}_data)
    assert response.status_code == 200
    assert response.json()["name"] == ${feature}_data["name"]
    # TODO: Add more assertions

def test_update_${feature}():
    """Test updating a ${feature}"""
    # TODO: Create a test ${feature} first
    ${feature}_id = 1
    update_data = {
        "name": "Updated ${feature^}",
        "description": "Updated description"
    }
    response = client.put(f"/${feature}/{${feature}_id}", json=update_data)
    assert response.status_code == 200
    assert response.json()["name"] == update_data["name"]
    # TODO: Add more assertions

def test_delete_${feature}():
    """Test deleting a ${feature}"""
    # TODO: Create a test ${feature} first
    ${feature}_id = 1
    response = client.delete(f"/${feature}/{${feature}_id}")
    assert response.status_code == 200
    assert response.json()["message"] == "${feature^} deleted"
    # TODO: Verify ${feature} was actually deleted
EOF
        echo "Created test file: tests/test_${feature}.py"
    else
        echo "Test file already exists: tests/test_${feature}.py"
    fi
}

# Function to create React component files
function create_ui_files {
    local feature=$1
    
    # Create React component files
    mkdir -p frontend/src/pages/${feature^}
    
    if [ ! -f "frontend/src/pages/${feature^}/index.tsx" ]; then
        cat > "frontend/src/pages/${feature^}/index.tsx" << EOF
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useParams, useNavigate } from 'react-router-dom';
import './styles.css';

interface ${feature^}Data {
  id: number;
  name: string;
  description: string;
  created_at: string;
  updated_at: string | null;
}

const ${feature^}: React.FC = () => {
  const [data, setData] = useState<${feature^}Data[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetch${feature^}Data = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/${feature}');
        setData(response.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching ${feature} data:', err);
        setError('Failed to load ${feature} data. Please try again later.');
        // For development, use mock data when API is not available
        setData([
          { 
            id: 1, 
            name: 'Sample ${feature^}', 
            description: 'This is a sample ${feature}',
            created_at: new Date().toISOString(),
            updated_at: null
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetch${feature^}Data();
  }, []);

  if (loading) {
    return <div>Loading ${feature} data...</div>;
  }

  if (error) {
    return (
      <div>
        <p>{error}</p>
        <h2>Sample ${feature^} Data (Mock)</h2>
        <ul>
          {data.map(item => (
            <li key={item.id}>
              {item.name} - {item.description}
            </li>
          ))}
        </ul>
      </div>
    );
  }

  return (
    <div className="${feature}-container">
      <h1>${feature^} Dashboard</h1>
      <p>This is the ${feature} management interface.</p>
      
      <h2>${feature^} List</h2>
      {data.length === 0 ? (
        <p>No ${feature} data found.</p>
      ) : (
        <ul className="${feature}-list">
          {data.map(item => (
            <li key={item.id} className="${feature}-item">
              <h3>{item.name}</h3>
              <p>{item.description}</p>
              <div className="${feature}-meta">
                <span>Created: {new Date(item.created_at).toLocaleDateString()}</span>
                {item.updated_at && (
                  <span>Updated: {new Date(item.updated_at).toLocaleDateString()}</span>
                )}
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ${feature^};
EOF
        echo "Created React component file: frontend/src/pages/${feature^}/index.tsx"
    else
        echo "React component file already exists: frontend/src/pages/${feature^}/index.tsx"
    fi
    
    if [ ! -f "frontend/src/pages/${feature^}/styles.css" ]; then
        cat > "frontend/src/pages/${feature^}/styles.css" << EOF
.${feature}-container {
  padding: 20px;
}

.${feature}-list {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.${feature}-item {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 15px;
  margin-bottom: 15px;
  background-color: white;
}

.${feature}-item h3 {
  margin-top: 0;
  color: var(--primary-color);
}

.${feature}-meta {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-small);
  color: #666;
  margin-top: 10px;
}
EOF
        echo "Created CSS file: frontend/src/pages/${feature^}/styles.css"
    else
        echo "CSS file already exists: frontend/src/pages/${feature^}/styles.css"
    fi
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

action=""
feature=""

while [ $# -gt 0 ]; do
    case "$1" in
        --start-api)
            action="start-api"
            shift
            ;;
        --start-test)
            action="start-test"
            shift
            ;;
        --start-ui)
            action="start-ui"
            shift
            ;;
        --complete-api)
            action="complete-api"
            shift
            ;;
        --complete-test)
            action="complete-test"
            shift
            ;;
        --complete-ui)
            action="complete-ui"
            shift
            ;;
        --status)
            show_status
            exit 0
            ;;
        --help)
            display_usage
            exit 0
            ;;
        -*)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
        *)
            feature=$1
            shift
            ;;
    esac
done

if [ "$action" != "" ] && [ "$feature" == "" ]; then
    echo "Error: No feature name specified"
    display_usage
    exit 1
fi

case "$action" in
    start-api)
        update_status "$feature" "api" "In Progress"
        create_api_files "$feature"
        ;;
    start-test)
        update_status "$feature" "test" "In Progress"
        create_test_files "$feature"
        ;;
    start-ui)
        update_status "$feature" "ui" "In Progress"
        create_ui_files "$feature"
        ;;
    complete-api)
        update_status "$feature" "api" "Complete"
        ;;
    complete-test)
        update_status "$feature" "test" "Complete"
        ;;
    complete-ui)
        update_status "$feature" "ui" "Complete"
        # Update Nginx routes to point to React implementation
        ./.dockerwrapper/update-routes.sh --add-react-route "/$feature"
        echo "Added React route for /$feature"
        echo "You need to restart Nginx for changes to take effect:"
        echo "./.dockerwrapper/run.sh restart nginx"
        ;;
    *)
        echo "Error: No action specified"
        display_usage
        exit 1
        ;;
esac 