# Debug History Database

This directory maintains a structured history of debugging sessions and their resolutions:

## Directory Structure

- `debug_sessions.json` - Log of debugging sessions with error-solution pairs
- `error_categories.json` - Error categorization by component and type
- `context_versions.json` - Context and code versions for fixes

## Purpose

This history helps Claude:
1. Track debugging patterns
2. Reference previous solutions
3. Understand error contexts
4. Maintain debugging knowledge
