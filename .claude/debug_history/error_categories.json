{"version": "1.0.0", "last_updated": "2025-03-06", "categories": {"server": {"port_binding": {"description": "Issues with server port configuration and binding", "common_causes": ["Port already in use", "Invalid port mapping", "Container port restrictions"], "related_components": ["Flask UI", "FastAPI Backend"]}, "stability": {"description": "Server stability and uptime issues", "common_causes": ["Resource constraints", "Configuration conflicts", "Improper shutdown handling"], "related_components": ["Flask UI", "FastAPI Backend"]}}, "database": {"connection": {"description": "Database connection and authentication issues", "common_causes": ["Invalid connection string", "Missing credentials", "Connection pool exhaustion"], "related_components": ["SQLAlchemy", "PostgreSQL"]}}}}