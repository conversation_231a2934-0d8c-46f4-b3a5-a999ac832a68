# PostgreSQL Database Standards for RepoTimeline

This document outlines the PostgreSQL database standards and best practices to follow for the RepoTimeline project.

## Database Design Principles

- Follow normalization principles (aim for 3NF in most cases)
- Use appropriate data types for columns
- Define explicit primary keys for all tables
- Use foreign keys to enforce referential integrity
- Include appropriate indexes for query optimization
- Use constraints to enforce data integrity

## Naming Conventions

- Use snake_case for table and column names
- Use plural names for tables (e.g., `repositories`, not `repository`)
- Use singular names for junction tables (e.g., `repository_user`)
- Prefix junction table names with the names of the tables they connect
- Use descriptive names for constraints and indexes
- Follow a consistent pattern for primary keys (e.g., `id` or `table_name_id`)

## Data Types

- Use appropriate PostgreSQL data types:
  - `TEXT` for variable-length strings with no upper limit
  - `VARCHAR(n)` for variable-length strings with a limit
  - `INTEGER` for whole numbers
  - `BIGINT` for large whole numbers
  - `DECIMAL` or `NUMERIC` for precise decimal values
  - `TIMESTAMP WITH TIME ZONE` for date and time values
  - `BOOLEAN` for true/false values
  - `JSONB` for JSON data
  - `UUID` for universally unique identifiers

## Indexes

- Add indexes to columns frequently used in WHERE clauses
- Add indexes to foreign key columns
- Consider partial indexes for filtered queries
- Use composite indexes for queries with multiple conditions
- Be mindful of index overhead for write-heavy tables

## Migrations

- Use Alembic for all database migrations
- Write both upgrade and downgrade functions
- Keep migrations small and focused
- Include descriptive comments in migration files
- Test migrations thoroughly before applying to production

## Query Performance

- Use EXPLAIN ANALYZE to understand query performance
- Avoid N+1 query problems by using JOINs and eager loading
- Use appropriate indexes for query patterns
- Consider materialized views for complex, frequently-accessed data
- Implement connection pooling

## Security

- Use parameterized queries to prevent SQL injection
- Implement row-level security where appropriate
- Use least privilege principle for database users
- Encrypt sensitive data
- Regularly backup the database

## Example Schema

```mermaid
erDiagram
    REPOSITORIES ||--o{ SPECIAL_FILES : contains
    REPOSITORIES ||--o{ COMMITS : has
    REPOSITORIES ||--o{ MILESTONES : tracks
    
    REPOSITORIES {
        int id PK
        string name
        string full_name
        text description
        string url
        string html_url
        string clone_url
        string default_branch
        int stars
        int forks
        int open_issues
        boolean is_active
        timestamp created_at
        timestamp updated_at
        timestamp last_synced_at
    }
    
    SPECIAL_FILES {
        int id PK
        int repository_id FK
        string file_type
        string file_path
        text content
        timestamp created_at
        timestamp updated_at
    }
    
    COMMITS {
        int id PK
        int repository_id FK
        string sha
        text message
        string author
        string author_email
        timestamp committed_date
        timestamp created_at
    }
    
    MILESTONES {
        int id PK
        int repository_id FK
        string title
        text description
        timestamp due_date
        boolean is_completed
        timestamp completed_date
        timestamp created_at
        timestamp updated_at
    }
```

## SQLAlchemy Usage

- Use declarative models with SQLAlchemy ORM
- Define relationships between models
- Use sessions for transaction management
- Implement proper error handling
- Use migrations for schema changes

## Connection Management

- Use connection pooling
- Properly close connections after use
- Use transactions for related operations
- Implement retry logic for transient failures
- Monitor connection usage and performance 