# Project Guidelines

## Project Overview
A comprehensive cybersecurity data platform providing:
- Advanced security analysis 
- Real-time system monitoring
- Intelligent threat detection 
- Robust user management and authentication
- MITRE ATLAS (Adversarial Threat Landscape for AI Systems) integration

## Technology Stack
- FastAPI and Flask frameworks with centralized logging and route validation
- PostgreSQL database with SQLAlchemy 2.0 ORM integration 
- Comprehensive user authentication with social login options
- Pydantic type validation
- D3FEND and MITRE ATT&CK security framework integrations
- MITRE ATLAS framework with full technique/tactic mapping
- Responsive Material Design Bootstrap styling

## Development Guidelines

### Database Management
1. Use SQLAlchemy migrations for all database schema changes
2. Never manually write SQL migrations
3. Use Pydantic models for request/response validation
4. Follow SQLAlchemy 2.0 style with type annotations

### API Development
1. Use FastAPI for backend API endpoints
2. Implement comprehensive validation using Pydantic
3. Maintain consistent error handling across endpoints
4. Document all endpoints using FastAPI's built-in OpenAPI support

### Authentication & Security
1. Implement JWT-based authentication
2. Use proper password hashing (Werkzeug security)
3. Maintain secure session management
4. Follow OWASP security best practices

### Frontend Development
1. Use React/Vue for the web interface
2. Implement Material Design or Tailwind CSS components
3. Follow component-based architecture
4. Maintain responsive design principles
5. Use TypeScript for type safety
6. Implement state management (React Context/Redux for React or Pinia/Vuex for Vue)
7. Use modern frontend build tools (Vite, webpack)

### Testing
1. Write comprehensive unit tests with pytest
2. Maintain high code coverage (aim for >90%)
3. Use pytest fixtures for database testing
4. Mock external services appropriately

### Code Organization
1. Maintain clear module separation:
   - api/ - FastAPI backend
   - models/ - Database models
   - services/ - Business logic 
   - routes/ - Route handlers
   - schemas/ - Pydantic models
   - utils/ - Utilities
2. Follow PEP 8 style guidelines
3. Use type hints consistently

### Integration Guidelines
1. MITRE ATLAS Integration:
   - Import data via atlas_importer service
   - Store in dedicated atlas_* tables
   - Maintain technique-tactic relationships
   - Support technique versioning
   - Regular data synchronization
2. D3FEND Integration:
   - Map to MITRE ATT&CK framework
   - Maintain defensive technique relationships
   - Version control defensive patterns

## Development Workflow
1. Use Replit's built-in tools for development
2. Run tests before committing changes
3. Maintain documentation alongside code
4. Use proper logging for debugging
5. Follow GitFlow branching model

## Feature Development Process
1. Plan changes thoroughly
2. Write tests first
3. Implement features
4. Verify functionality
5. Document changes
6. Submit for review

## Production Guidelines
1. Use proper environment variables
2. Implement proper error handling
3. Set up monitoring and logging
4. Follow secure deployment practices
5. Maintain backup procedures

## Completed Features
1. ✓ MITRE ATLAS Integration
   - Database schema and models
   - Data import service
   - Technique/tactic relationships
   - Version management

## Future Features
1. ATLAS visualization using D3.js
2. ATLAS-specific search and filtering
3. AI threat intelligence dashboard
4. ATLAS-ATT&CK mapping capabilities
5. Automated AI threat detection rules