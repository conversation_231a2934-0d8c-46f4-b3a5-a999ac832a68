# Python Coding Standards for RepoTimeline

This document outlines the Python coding standards and PEPs to follow for the RepoTimeline project.

## PEP Recommendations

### Core PEPs to Follow

- **[PEP 8](https://www.python.org/dev/peps/pep-0008/)**: Style Guide for Python Code
  - Use 4 spaces for indentation
  - Maximum line length of 88 characters (compatible with Black)
  - Use snake_case for functions and variables
  - Use CamelCase for classes
  - Use UPPERCASE for constants

- **[PEP 257](https://www.python.org/dev/peps/pep-0257/)**: Docstring Conventions
  - Use triple double quotes for docstrings
  - Include a one-line summary, followed by a blank line, then detailed description
  - Document parameters, return values, and exceptions

- **[PEP 484](https://www.python.org/dev/peps/pep-0484/)**: Type Hints
  - Use type hints for all function parameters and return values
  - Use Optional[] for parameters that can be None
  - Use Union[] for parameters that can be multiple types

- **[PEP 526](https://www.python.org/dev/peps/pep-0526/)**: Variable Annotations
  - Use variable annotations for class attributes and variables

- **[PEP 585](https://www.python.org/dev/peps/pep-0585/)**: Type Hinting Generics In Standard Collections
  - Use built-in collection types for type hints (e.g., list[str] instead of List[str])

## FastAPI Best Practices

- Use Pydantic models for request and response validation
- Organize endpoints into routers by feature area
- Use dependency injection for database sessions and authentication
- Document API endpoints with appropriate descriptions and examples
- Use status codes consistently across endpoints

## Database Access Patterns

- Use SQLAlchemy ORM for database access
- Create repository classes for database operations
- Use async/await for database operations when possible
- Implement proper error handling and transaction management
- Use migrations for database schema changes

## Code Organization

```
app/
├── api/
│   ├── dependencies.py
│   ├── api_v1/
│   │   ├── endpoints/
│   │   │   ├── repositories.py
│   │   │   ├── auth.py
│   │   │   └── ...
│   │   └── api.py
├── core/
│   ├── config.py
│   ├── security.py
│   └── ...
├── db/
│   ├── base.py
│   ├── session.py
│   └── ...
├── models/
│   ├── repository.py
│   └── ...
├── schemas/
│   ├── repository.py
│   └── ...
├── services/
│   ├── github_service.py
│   └── ...
└── main.py
```

## Testing Standards

- Write unit tests for all services and utilities
- Write integration tests for API endpoints
- Use pytest for testing
- Aim for at least 80% code coverage

## Documentation

- Document all public functions, classes, and methods
- Include examples in docstrings where appropriate
- Keep documentation up-to-date with code changes

## Mermaid Diagrams

When documenting architecture or workflows, use Mermaid diagrams in Markdown files:

```mermaid
graph TD
    A[Client] --> B[FastAPI Backend]
    B --> C[Database]
    B --> D[GitHub API]
    D --> E[Repository Data]
    E --> B
``` 