{"version": "1.0.0", "last_updated": "2025-03-06", "patterns": {"flask_sqlalchemy_import": {"pattern": "ImportError: No module named flask_sqlalchemy", "solution": "Use packager_install_tool to install flask-sqlalchemy", "context": "Flask application initialization"}, "port_binding": {"pattern": "Address already in use", "solution": "Check if another service is using port 5000/5001, or restart the workflow", "context": "Server startup"}, "database_connection": {"pattern": "OperationalError: (psycopg2.OperationalError) could not connect to server", "solution": "Verify DATABASE_URL environment variable and database status", "context": "Database initialization"}}}