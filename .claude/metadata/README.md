# Claude Metadata Directory

This directory contains structured metadata to help Claude instances better understand and work with the codebase:

## Directory Structure

- `component_dependencies.json` - Machine-readable dependency graphs between components
- `file_classifications.json` - File type and purpose classifications
- `error_patterns.json` - Known error patterns and their solutions

## Purpose

This metadata helps Claude:
1. Understand component relationships
2. Quickly identify file purposes
3. Reference common error patterns and solutions
4. Maintain consistency across interactions
