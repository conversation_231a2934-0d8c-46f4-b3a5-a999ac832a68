{"version": "1.0.0", "last_updated": "2025-03-06", "components": {"flask_ui": {"path": "app.py", "depends_on": ["api.database", "api.models", "api.forms", "api.routes.auth"], "depended_by": [], "type": "web_interface"}, "fastapi_backend": {"path": "api/main.py", "depends_on": ["api.utils.logging_config"], "depended_by": [], "type": "api_service"}, "database": {"path": "api/database.py", "depends_on": ["sqlalchemy"], "depended_by": ["flask_ui", "fastapi_backend"], "type": "data_layer"}}}