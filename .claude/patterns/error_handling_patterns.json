{"version": "1.0.0", "last_updated": "2025-03-06", "patterns": {"api_error_handling": {"pattern_name": "API Error Handler", "context_preservation": {"request_context": ["method", "path", "headers"], "error_context": ["error_type", "message", "stack_trace"], "user_context": ["user_id", "session_id"]}, "example": {"description": "Comprehensive API error handling", "code": "@app.exception_handler(RequestValidationError)\nasync def validation_exception_handler(request: Request, exc: RequestValidationError):\n    return JSONResponse(\n        status_code=422,\n        content={\n            'detail': exc.errors(),\n            'body': exc.body,\n            'request_id': request.state.request_id\n        }\n    )"}}, "database_error_handling": {"pattern_name": "Database Error <PERSON>", "context_preservation": {"db_context": ["operation", "table", "parameters"], "error_context": ["error_code", "message", "constraint_violation"], "transaction_context": ["transaction_id", "isolation_level"]}, "example": {"description": "Database error handling with context", "code": "try:\n    async with db.transaction() as tx:\n        await tx.execute(query)\nexcept UniqueViolationError as e:\n    logger.error('Unique constraint violation', extra={\n        'table': e.table_name,\n        'constraint': e.constraint_name,\n        'detail': e.detail\n    })\n    raise HTTPException(status_code=409, detail='Resource already exists')"}}}}