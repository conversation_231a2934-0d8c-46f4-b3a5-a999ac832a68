{"version": "1.0.0", "last_updated": "2025-03-06", "patterns": {"api_endpoint": {"pattern_name": "REST API Endpoint", "template": {"validation": "Use Pydantic for request/response validation", "error_handling": "Include try-except with specific error types", "logging": "Log entry/exit points with correlation IDs"}, "example": {"description": "Standard REST endpoint implementation", "code": "from fastapi import HTTPException, Depends\nfrom pydantic import BaseModel\n\************('/{id}')\nasync def get_item(id: int) -> ItemResponse:\n    try:\n        item = await service.get_item(id)\n        return ItemResponse.from_orm(item)\n    except ItemNotFound as e:\n        raise HTTPException(status_code=404, detail=str(e))"}}, "database_transaction": {"pattern_name": "Database Transaction", "template": {"session_management": "Use context manager for sessions", "rollback": "Implement automatic rollback on errors", "retry": "Include retry logic for transient failures"}, "example": {"description": "Safe database transaction pattern", "code": "async with db.transaction() as tx:\n    try:\n        result = await tx.execute(query)\n        await tx.commit()\n    except Exception as e:\n        await tx.rollback()\n        raise DatabaseError(f'Transaction failed: {e}')"}}}}