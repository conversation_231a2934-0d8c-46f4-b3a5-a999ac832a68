{"version": "1.0.0", "last_updated": "2025-03-06", "patterns": {"service_composition": {"pattern_name": "Service Layer Composition", "reliability_metrics": {"error_rate": "Track error frequency and types", "latency": "Measure response time distribution", "availability": "Monitor service uptime"}, "example": {"description": "Service layer with reliability tracking", "code": "class UserService:\n    def __init__(self, metrics: MetricsCollector):\n        self.metrics = metrics\n\n    async def create_user(self, data: UserCreate) -> User:\n        with self.metrics.measure('user_creation'):\n            try:\n                user = await self.repository.create(data)\n                self.metrics.increment('user_creation_success')\n                return user\n            except Exception as e:\n                self.metrics.increment('user_creation_error')\n                raise"}}, "middleware_composition": {"pattern_name": "Middleware Chain", "reliability_metrics": {"chain_completion": "Track middleware chain execution", "error_propagation": "Monitor error handling flow", "performance_impact": "Measure middleware overhead"}, "example": {"description": "Middleware chain with metrics", "code": "@app.middleware('http')\nasync def reliability_middleware(request: Request, call_next):\n    start_time = time.time()\n    try:\n        response = await call_next(request)\n        duration = time.time() - start_time\n        metrics.histogram('http_request_duration', duration)\n        return response\n    except Exception as e:\n        metrics.increment('http_request_error')\n        raise"}}}}