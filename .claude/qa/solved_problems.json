{"version": "1.0.0", "last_updated": "2025-03-06", "problems": {"server_port_configuration": {"timestamp": "2025-03-06T09:45:00Z", "category": "server_setup", "problem": "FastAPI and Flask servers port configuration in Replit", "context": "Initial server setup had FastAPI using port 8080 which wasn't mapped in Replit", "solution": {"description": "Configure servers to use Replit-mapped ports", "steps": ["Set Flask UI server to port 5000 (mapped to 80)", "Set FastAPI backend to port 5001 (mapped to 3001)", "Remove cross-mounting between Flask and FastAPI"], "code_changes": {"app.py": "Removed FastAPI mounting, added proper __main__ entry", "api/main.py": "Updated port from 8080 to 5001"}, "verification": "Both servers running independently on correct ports"}}}}