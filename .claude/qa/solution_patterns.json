{"version": "1.0.0", "last_updated": "2025-03-06", "patterns": {"dual_server_setup": {"pattern_name": "Flask UI + FastAPI Backend", "description": "Pattern for running Flask and FastAPI servers together", "key_points": ["Use separate ports for each server", "Avoid cross-mounting applications", "Implement proper health checks", "Configure logging for both servers"], "implementation": {"Flask": {"port": 5000, "host": "0.0.0.0", "main_guard": true}, "FastAPI": {"port": 5001, "host": "0.0.0.0", "lifespan": true}}}, "database_initialization": {"pattern_name": "Safe Database Setup", "description": "Pattern for reliable database initialization", "key_points": ["Configure before importing models", "Use appropriate connection pools", "Implement health checks", "Handle migration gracefully"], "implementation": {"connection_pool": {"recycle": 300, "pre_ping": true}, "init_order": ["Configure app", "Set database URI", "Initialize extensions", "Import models", "Create tables"]}}}}