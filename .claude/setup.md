# Development Setup Guide

## Prerequisites
- Python 3.11+
- PostgreSQL database
- Git

## Environment Setup
1. **Database Configuration**
   ```bash
   # Database URL format:
   DATABASE_URL=postgresql://user:password@host:port/dbname
   ```

2. **Required Environment Variables**
   - `DATABASE_URL`: PostgreSQL connection string
   - `SESSION_SECRET`: Secret key for session management
   - `JWT_SECRET_KEY`: Secret for JWT token generation
   - `JWT_ALGORITHM`: Algorithm for JWT (default: HS256)

## Project Structure
```
.
├── api/                    # FastAPI backend
│   ├── models/            # SQLAlchemy models
│   ├── routes/            # API routes
│   ├── schemas/           # Pydantic schemas
│   ├── services/          # Business logic
│   └── utils/             # Utilities
├── migrations/            # Database migrations
├── tests/                 # Test suite
├── docs/                  # Documentation
└── 3rdparty/             # Third-party dependencies
    └── atlas-navigator-data/  # MITRE ATLAS data
```

## Development Server
1. **FastAPI Backend**
   - Port: 5001
   - Auto-reload enabled
   - Swagger UI: /docs
   - ReDoc: /redoc

2. **Flask Frontend**
   - Port: 5000
   - Debug mode enabled
   - Template auto-reload

## Database Management
1. **Initial Setup**
   ```python
   # Initialize database
   from api.database import init_db
   init_db()
   ```

2. **Migrations**
   ```python
   # Create migration
   flask db migrate -m "description"
   
   # Apply migration
   flask db upgrade
   ```

## Testing
1. **Run Tests**
   ```bash
   pytest tests/
   ```

2. **Coverage Report**
   ```bash
   pytest --cov=api tests/
   ```

## MITRE ATLAS Integration
1. **Data Import**
   ```bash
   # Import ATLAS data
   python -m api.cli.atlas_commands import-data
   ```

2. **Update ATLAS Data**
   ```bash
   # Pull latest ATLAS data
   git submodule update --remote 3rdparty/atlas-navigator-data
   ```

## Code Style
1. **Type Checking**
   ```bash
   mypy api/
   ```

2. **Linting**
   ```bash
   flake8 api/
   ```

## Documentation
1. **API Documentation**
   - Generated automatically by FastAPI
   - Available at /docs and /redoc

2. **Code Documentation**
   - Use Google-style docstrings
   - Keep README.md updated
   - Document all public interfaces

## Debugging
1. **Logging**
   - Level controlled by LOG_LEVEL env var
   - Default: INFO in prod, DEBUG in dev
   - Logs stored in logs/ directory

2. **Debug Mode**
   - Enabled by default in development
   - Enhanced error pages
   - Auto-reload on code changes
