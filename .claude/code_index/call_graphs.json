{"version": "1.0.0", "last_updated": "2025-03-06", "call_graphs": {"api/main.py": {"create_app": {"calls": ["lifespan", "setup_logging"], "called_by": ["__main__"]}, "lifespan": {"calls": ["setup_logging"], "called_by": ["create_app"]}, "root": {"calls": ["datetime.utcnow"], "called_by": ["FastAPI router"]}, "health_check": {"calls": ["datetime.utcnow"], "called_by": ["FastAPI router"]}}, "app.py": {"index": {"calls": ["render_template"], "called_by": ["Flask router"]}, "register": {"calls": ["RegistrationForm", "User.set_password", "db.session.add", "db.session.commit", "flash", "redirect", "url_for"], "called_by": ["Flask router"]}, "health": {"calls": ["logging.debug"], "called_by": ["Flask router"]}}}}