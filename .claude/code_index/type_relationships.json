{"version": "1.0.0", "last_updated": "2025-03-06", "type_relationships": {"models": {"User": {"inherits_from": ["db.<PERSON>", "UserMixin"], "implements": [], "attributes": ["id", "username", "email", "password_hash"], "methods": ["set_password", "check_password"]}}, "forms": {"RegistrationForm": {"inherits_from": ["FlaskForm"], "implements": [], "fields": ["username", "email", "password", "confirm_password"], "validators": ["DataRequired", "Email", "EqualTo"]}}, "api": {"FastAPI": {"inherits_from": ["Starlette"], "implements": [], "middleware": ["WSGIMiddleware"], "dependencies": ["Jinja2Templates", "StaticFiles"]}}}}