# Code Index Directory

This directory contains pre-analyzed semantic relationships and code structure information:

## Directory Structure

- `call_graphs.json` - Function-to-function call relationships
- `type_relationships.json` - Type inheritance and interface implementations
- `intent_classifications.json` - Purpose and intent of code sections

## Purpose

This index helps Claude:
1. Navigate function call chains
2. Understand type hierarchies
3. Identify code purposes
4. Speed up code comprehension
