# RepoTimeline Project Guidelines

This document outlines project-specific guidelines and conventions for the RepoTimeline application.

## Project Overview

RepoTimeline is a dashboard for tracking and visualizing the progress of private GitHub repositories. It provides a centralized view of key project features, improvements, and metrics across multiple repositories.

## Special Files Handling

The project needs to handle three special configuration files:

### .dockerwrapper

- Parse and extract infrastructure management configurations
- Display Docker configuration details in the repository view
- Track changes to Docker configurations over time

```mermaid
graph TD
    A[.dockerwrapper File] --> B[Parse Configuration]
    B --> C[Extract Services]
    B --> D[Extract Volumes]
    B --> E[Extract Networks]
    C --> F[Display in UI]
    D --> F
    E --> F
```

### .claude

- Parse guidance documents for coding standards
- Extract PEP recommendations and project-specific guidelines
- Display coding standards in the repository view

```mermaid
graph TD
    A[.claude Folder] --> B[Parse Markdown Files]
    B --> C[Extract Coding Standards]
    B --> D[Extract Best Practices]
    C --> E[Display in UI]
    D --> E
```

### .cursor

- Parse cursor-specific rules and configurations
- Extract editor settings and preferences
- Display cursor configurations in the repository view

```mermaid
graph TD
    A[.cursor File] --> B[Parse Configuration]
    B --> C[Extract Editor Settings]
    B --> D[Extract Mermaid Rules]
    C --> E[Display in UI]
    D --> E
```

## Timeline Visualization

The timeline visualization should:

- Show repository activity over time
- Highlight key milestones and features
- Display commit frequency and distribution
- Allow filtering by time period and activity type
- Support zooming and panning

```mermaid
gantt
    title Repository Timeline Example
    dateFormat  YYYY-MM-DD
    section Features
    Initial Setup           :done,    des1, 2023-01-01, 2023-01-15
    User Authentication     :done,    des2, 2023-01-16, 2023-01-31
    Repository Integration  :active,  des3, 2023-02-01, 2023-02-28
    Analytics Dashboard     :         des4, 2023-03-01, 2023-03-31
    Advanced Reporting      :         des5, 2023-04-01, 2023-04-30
```

## Metrics and KPIs

Track the following metrics for each repository:

- Commit frequency (daily, weekly, monthly)
- Issue resolution time
- Pull request review time
- Code additions/deletions
- Build success rate
- Test coverage
- Documentation completeness

## Repository Selection Criteria

When selecting repositories to track:

- Focus on active projects with regular commits
- Prioritize repositories with the special configuration files
- Consider repositories with clear milestones and features
- Include a mix of backend and frontend projects
- Select repositories that represent different stages of development

## Data Refresh Strategy

- Sync repository data hourly via scheduled background jobs
- Implement webhook support for real-time updates when available
- Cache frequently accessed data to improve performance
- Implement incremental updates to minimize API usage
- Provide manual refresh option for immediate updates

## User Experience Guidelines

- Ensure consistent design across all pages
- Implement responsive design for all screen sizes
- Use clear and intuitive navigation
- Provide helpful tooltips and documentation
- Implement proper error handling and user feedback
- Support keyboard navigation and accessibility features

## Deployment Strategy

- Use Docker containers for all components
- Implement CI/CD pipeline for automated testing and deployment
- Support development, staging, and production environments
- Use environment variables for configuration
- Implement proper logging and monitoring

## Documentation Requirements

- Maintain up-to-date API documentation
- Document all features and functionality
- Include setup and deployment instructions
- Provide user guides and tutorials
- Document database schema and migrations 