# FastAPI Backend Quick Reference

## Common Operations

### Endpoint Definition
```python
@app.get("/")
async def root():
    return {"message": "Welcome"}
```

### Request Validation
```python
from pydantic import BaseModel

class Item(BaseModel):
    name: str
    price: float
```

## Known Pitfalls

1. Port Configuration
   - Use port 5001 for FastAPI backend
   - Check port mappings in .replit

2. CORS Settings
   - Configure CORS before adding routes
   - Allow specific origins only

3. Dependencies
   - Define dependencies before routes
   - Use Depends for injection

## Edge Cases

1. Error Handling
   - Use FastAPI exception handlers
   - Return proper status codes

2. Authentication
   - Validate JWT tokens properly
   - Handle token expiration

## Gotchas

1. Async Operations
   - Use async/await consistently
   - Don't mix sync and async code

2. Response Models
   - Define response_model in decorators
   - Handle circular references
