# Component Cheat Sheets

This directory contains quick-reference guides for each major component of the system:

## Directory Structure

- `flask_ui.md` - Quick reference for Flask UI components and routes
- `fastapi_backend.md` - FastAPI endpoints and middleware guide
- `database_operations.md` - Common database operations and best practices

## Purpose

These cheat sheets help Claude:
1. Quickly access common operations
2. Reference known pitfalls
3. Handle edge cases
4. Navigate component-specific gotchas
