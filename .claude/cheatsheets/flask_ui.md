# Flask UI Quick Reference

## Common Operations

### Route Registration
```python
@app.route('/', methods=['GET'])
def index():
    return render_template('index.html')
```

### Form Handling
```python
@app.route('/register', methods=['GET', 'POST'])
def register():
    form = RegistrationForm()
    if form.validate_on_submit():
        # Handle form submission
```

## Known Pitfalls

1. Port Configuration
   - ALWAYS use port 5000 and host '0.0.0.0'
   - Do not change port mappings in .replit

2. Database Initialization
   - Initialize SQLAlchemy after app configuration
   - Set SQLALCHEMY_TRACK_MODIFICATIONS = False

3. Authentication
   - Initialize LoginManager before routes
   - Use @login_required decorator carefully

## Edge Cases

1. Session Management
   - Handle session expiry gracefully
   - Clear session data on logout

2. Form Validation
   - Validate file uploads separately
   - Handle CSRF token expiry

## Gotchas

1. Template Loading
   - Templates must be in templates/ directory
   - Case-sensitive template names

2. Static Files
   - Use url_for('static', filename='...') 
   - Static files must be in static/ directory
