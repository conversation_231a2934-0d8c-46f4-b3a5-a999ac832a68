# Database Operations Quick Reference

## Common Operations

### Query Execution
```python
# Select
result = db.session.query(User).filter_by(username=username).first()

# Insert
db.session.add(new_user)
db.session.commit()
```

### Transaction Management
```python
try:
    db.session.begin()
    # operations
    db.session.commit()
except:
    db.session.rollback()
```

## Known Pitfalls

1. Connection Management
   - Use pool_pre_ping=True
   - Set appropriate pool_recycle

2. Migration Handling
   - Use Flask-Migrate for changes
   - Never modify tables directly

3. Foreign Keys
   - Define relationships properly
   - Handle cascade deletes

## Edge Cases

1. Concurrent Access
   - Handle deadlocks properly
   - Use appropriate isolation levels

2. Data Integrity
   - Validate before commit
   - Handle unique constraints

## Gotchas

1. Session Management
   - Close sessions properly
   - Don't share sessions

2. Model Updates
   - Refresh objects after commit
   - Handle expired objects
