# RepoTimeline Test Suite

This directory contains the test suite for the RepoTimeline frontend application. The tests are organized into unit tests and integration tests to ensure the application functions correctly.

## Test Structure

```
__tests__/
├── setup.ts                 # Global test setup and mocks
├── unit/                    # Unit tests for individual components and services
│   ├── RepositoryAnalytics.test.tsx
│   ├── repositoryService.test.ts
│   ├── commitService.test.ts
│   ├── milestoneService.test.ts
│   ├── specialFileService.test.ts
│   └── userService.test.ts
└── integration/             # Integration tests for combined functionality
```

## Running Tests

You can run the tests using the following npm scripts:

```bash
# Run all tests
npm test

# Run tests in watch mode (for development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

## Test Configuration

The test environment is configured using:

- `jest.config.js` - Main Jest configuration
- `__tests__/setup.ts` - Global test setup and mocks
- `__mocks__/` - Mock implementations for external dependencies
- `types/jest.d.ts` - TypeScript type definitions for Jest

## Testing Approach

### Unit Tests

Unit tests focus on testing individual components and services in isolation. For services, we mock the API calls using Jest's mocking capabilities to ensure we're only testing the service logic.

Example:
```typescript
// Testing a service
test('fetches repositories successfully', async () => {
  const repositories = [{ id: 1, name: 'repo1' }];
  mockedAxios.get.mockResolvedValueOnce({ data: repositories });
  
  const result = await repositoryService.getAllRepositories();
  
  expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/repositories', {
    params: { include_deleted: false }
  });
  expect(result).toEqual(repositories);
});

// Testing a component
test('renders loading state initially', async () => {
  render(<RepositoryAnalytics repositoryId={1} />);
  expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
});
```

### Integration Tests

Integration tests verify that different parts of the application work together correctly. These tests often involve multiple components and may include API interactions.

## Test Coverage

We aim for a minimum of 70% test coverage across the codebase. Coverage reports can be generated using:

```bash
npm run test:coverage
```

## Mocking Strategy

External dependencies are mocked to isolate the code being tested:

- **API Calls**: Using Jest's mock for axios
- **Browser APIs**: Mocking window objects, localStorage, etc.
- **React Router**: Mocking navigation and route parameters
- **External Libraries**: Mocking chart libraries and other dependencies

## Best Practices

1. **Test Isolation**: Each test should be independent and not rely on the state from other tests
2. **Descriptive Test Names**: Use clear, descriptive names that explain what is being tested
3. **Arrange-Act-Assert**: Structure tests with setup, action, and verification phases
4. **Mock External Dependencies**: Avoid real API calls or external services in tests
5. **Test Edge Cases**: Include tests for error handling and edge cases
6. **Keep Tests Fast**: Tests should run quickly to encourage frequent testing

## Adding New Tests

When adding new features, follow these steps to add tests:

1. Determine if you need a unit test or integration test
2. Create a new test file in the appropriate directory
3. Import the necessary testing utilities and the code to be tested
4. Write tests that cover the main functionality and edge cases
5. Run the tests to ensure they pass
6. Check test coverage to ensure adequate coverage 