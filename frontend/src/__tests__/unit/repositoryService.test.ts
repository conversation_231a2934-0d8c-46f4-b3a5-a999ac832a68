import axios from 'axios';
import { repositoryService } from '../../services';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Repository Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getAllRepositories', () => {
    test('fetches repositories successfully', async () => {
      const repositories = [
        { id: 1, name: 'repo1' },
        { id: 2, name: 'repo2' }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: repositories });
      
      const result = await repositoryService.getAllRepositories();
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/repositories', {
        params: { include_deleted: false }
      });
      expect(result).toEqual(repositories);
    });

    test('includes deleted repositories when specified', async () => {
      const repositories = [
        { id: 1, name: 'repo1', is_deleted: false },
        { id: 2, name: 'repo2', is_deleted: true }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: repositories });
      
      const result = await repositoryService.getAllRepositories(true);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/repositories', {
        params: { include_deleted: true }
      });
      expect(result).toEqual(repositories);
    });

    test('handles errors correctly', async () => {
      const errorMessage = 'Network Error';
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(repositoryService.getAllRepositories()).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/repositories', {
        params: { include_deleted: false }
      });
    });
  });

  describe('getDeletedRepositories', () => {
    test('fetches deleted repositories successfully', async () => {
      const repositories = [
        { id: 1, name: 'repo1', is_deleted: true },
        { id: 2, name: 'repo2', is_deleted: true }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: repositories });
      
      const result = await repositoryService.getDeletedRepositories();
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/repositories/deleted');
      expect(result).toEqual(repositories);
    });

    test('handles errors correctly', async () => {
      const errorMessage = 'Network Error';
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(repositoryService.getDeletedRepositories()).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/repositories/deleted');
    });
  });

  describe('getRepositoryById', () => {
    test('fetches a repository by ID successfully', async () => {
      const repository = { id: 1, name: 'repo1' };
      
      mockedAxios.get.mockResolvedValueOnce({ data: repository });
      
      const result = await repositoryService.getRepositoryById(1);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/repositories/1', {
        params: { include_deleted: false }
      });
      expect(result).toEqual(repository);
    });

    test('includes deleted repository when specified', async () => {
      const repository = { id: 1, name: 'repo1', is_deleted: true };
      
      mockedAxios.get.mockResolvedValueOnce({ data: repository });
      
      const result = await repositoryService.getRepositoryById(1, true);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/repositories/1', {
        params: { include_deleted: true }
      });
      expect(result).toEqual(repository);
    });

    test('handles errors correctly', async () => {
      const errorMessage = 'Repository not found';
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(repositoryService.getRepositoryById(999)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/repositories/999', {
        params: { include_deleted: false }
      });
    });
  });

  describe('createRepository', () => {
    test('creates a repository successfully', async () => {
      const repositoryData = {
        name: 'new-repo',
        full_name: 'user/new-repo',
        description: 'A new repository'
      };
      
      const createdRepository = {
        id: 1,
        ...repositoryData,
        created_at: '2023-01-01T00:00:00Z'
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: createdRepository });
      
      const result = await repositoryService.createRepository(repositoryData);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/repositories', repositoryData);
      expect(result).toEqual(createdRepository);
    });

    test('handles errors correctly', async () => {
      const repositoryData = {
        name: 'new-repo',
        full_name: 'user/new-repo',
        description: 'A new repository'
      };
      
      const errorMessage = 'Failed to create repository';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(repositoryService.createRepository(repositoryData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/repositories', repositoryData);
    });
  });

  describe('updateRepository', () => {
    test('updates a repository successfully', async () => {
      const repositoryId = 1;
      const updateData = {
        description: 'Updated description'
      };
      
      const updatedRepository = {
        id: repositoryId,
        name: 'repo1',
        description: 'Updated description',
        updated_at: '2023-01-01T00:00:00Z'
      };
      
      mockedAxios.put.mockResolvedValueOnce({ data: updatedRepository });
      
      const result = await repositoryService.updateRepository(repositoryId, updateData);
      
      expect(mockedAxios.put).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}`, updateData);
      expect(result).toEqual(updatedRepository);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      const updateData = {
        description: 'Updated description'
      };
      
      const errorMessage = 'Failed to update repository';
      mockedAxios.put.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(repositoryService.updateRepository(repositoryId, updateData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.put).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}`, updateData);
    });
  });

  describe('deleteRepository', () => {
    test('soft-deletes a repository successfully', async () => {
      const repositoryId = 1;
      
      mockedAxios.delete.mockResolvedValueOnce({ data: {} });
      
      await repositoryService.deleteRepository(repositoryId);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}`, {
        params: { permanent: false }
      });
    });

    test('permanently deletes a repository when specified', async () => {
      const repositoryId = 1;
      
      mockedAxios.delete.mockResolvedValueOnce({ data: {} });
      
      await repositoryService.deleteRepository(repositoryId, true);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}`, {
        params: { permanent: true }
      });
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      
      const errorMessage = 'Failed to delete repository';
      mockedAxios.delete.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(repositoryService.deleteRepository(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}`, {
        params: { permanent: false }
      });
    });
  });

  describe('restoreRepository', () => {
    test('restores a repository successfully', async () => {
      const repositoryId = 1;
      const restoredRepository = {
        id: repositoryId,
        name: 'repo1',
        is_deleted: false,
        deleted_at: null
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: restoredRepository });
      
      const result = await repositoryService.restoreRepository(repositoryId);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/restore`);
      expect(result).toEqual(restoredRepository);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      
      const errorMessage = 'Failed to restore repository';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(repositoryService.restoreRepository(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/restore`);
    });
  });

  describe('getFileTypeAnalytics', () => {
    test('fetches file type analytics successfully', async () => {
      const repositoryId = 1;
      const analyticsData = [
        { type: 'dockerwrapper', count: 5 },
        { type: 'claude', count: 3 },
        { type: 'cursor', count: 7 }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: analyticsData });
      
      const result = await repositoryService.getFileTypeAnalytics(repositoryId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/analytics/files`);
      expect(result).toEqual(analyticsData);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      
      const errorMessage = 'Failed to fetch analytics';
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(repositoryService.getFileTypeAnalytics(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/analytics/files`);
    });
  });

  describe('exportRepositoryData', () => {
    test('exports repository data as CSV successfully', async () => {
      const repositoryId = 1;
      const format = 'csv';
      const mockBlob = new Blob(['mock csv data'], { type: 'text/csv' });
      
      // Mock window.URL.createObjectURL
      const mockCreateObjectURL = jest.fn();
      global.URL.createObjectURL = mockCreateObjectURL;
      mockCreateObjectURL.mockReturnValueOnce('blob:mock-url');
      
      // Mock document.createElement and related methods
      const mockLink = {
        href: '',
        setAttribute: jest.fn(),
        click: jest.fn(),
        remove: jest.fn()
      };
      document.createElement = jest.fn().mockReturnValueOnce(mockLink);
      document.body.appendChild = jest.fn();
      
      mockedAxios.get.mockResolvedValueOnce({ data: mockBlob });
      
      await repositoryService.exportRepositoryData(repositoryId, format);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/export`, {
        params: { format },
        responseType: 'blob'
      });
      
      expect(mockCreateObjectURL).toHaveBeenCalled();
      expect(mockLink.setAttribute).toHaveBeenCalledWith('download', `repository-${repositoryId}.${format}`);
      expect(mockLink.click).toHaveBeenCalled();
      expect(mockLink.remove).toHaveBeenCalled();
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      const format = 'csv';
      
      const errorMessage = 'Failed to export data';
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(repositoryService.exportRepositoryData(repositoryId, format)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/export`, {
        params: { format },
        responseType: 'blob'
      });
    });
  });
}); 