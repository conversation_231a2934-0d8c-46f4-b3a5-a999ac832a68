import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import Dashboard from '../../../pages/Dashboard';
import { AuthProvider } from '../../../contexts/AuthContext';
import * as api from '../../../services/api';

// Mock the API service
jest.mock('../../../services/api');
const mockedApi = api as jest.Mocked<typeof api>;

// Mock the AuthContext
const mockAuthContext = {
  user: {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>'
  },
  isAuthenticated: true,
  logout: jest.fn()
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext,
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ConfigProvider>
      <BrowserRouter>
        <AuthProvider>
          {component}
        </AuthProvider>
      </BrowserRouter>
    </ConfigProvider>
  );
};

const mockRepositories = [
  {
    id: 1,
    name: 'test-repo-1',
    full_name: 'user/test-repo-1',
    description: 'Test repository 1',
    stars: 10,
    forks: 5,
    open_issues: 2,
    is_active: true,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-02T00:00:00Z'
  },
  {
    id: 2,
    name: 'test-repo-2',
    full_name: 'user/test-repo-2',
    description: 'Test repository 2',
    stars: 20,
    forks: 8,
    open_issues: 1,
    is_active: true,
    created_at: '2023-01-03T00:00:00Z',
    updated_at: '2023-01-04T00:00:00Z'
  }
];

const mockMilestones = [
  {
    id: 1,
    repository_id: 1,
    title: 'Version 1.0',
    description: 'First release',
    due_date: '2024-12-31T23:59:59Z',
    is_completed: false,
    created_at: '2023-01-01T00:00:00Z'
  }
];

const mockSpecialFiles = [
  {
    id: 1,
    repository_id: 1,
    file_type: 'dockerwrapper',
    file_path: '.dockerwrapper',
    content: 'wrapper content',
    created_at: '2023-01-01T00:00:00Z'
  }
];

describe('Dashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default API mocks
    mockedApi.default.get.mockImplementation((url: string) => {
      switch (url) {
        case '/api/v1/repositories':
          return Promise.resolve({ data: mockRepositories });
        case '/api/v1/milestones':
          return Promise.resolve({ data: mockMilestones });
        case '/api/v1/special-files':
          return Promise.resolve({ data: mockSpecialFiles });
        default:
          return Promise.reject(new Error('Not found'));
      }
    });
  });

  test('renders dashboard with loading state initially', () => {
    renderWithProviders(<Dashboard />);
    
    expect(screen.getByTestId('loading-indicator')).toBeInTheDocument();
  });

  test('displays dashboard statistics after loading', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('Total Repositories')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument(); // Total repositories count
      expect(screen.getByText('Special Files')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument(); // Special files count
      expect(screen.getByText('Milestones')).toBeInTheDocument();
    });
  });

  test('displays repository list', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('test-repo-1')).toBeInTheDocument();
      expect(screen.getByText('test-repo-2')).toBeInTheDocument();
      expect(screen.getByText('Test repository 1')).toBeInTheDocument();
      expect(screen.getByText('Test repository 2')).toBeInTheDocument();
    });
  });

  test('displays repository statistics in table', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      // Check for stars, forks, and issues
      expect(screen.getByText('10')).toBeInTheDocument(); // Stars for repo 1
      expect(screen.getByText('20')).toBeInTheDocument(); // Stars for repo 2
      expect(screen.getByText('5')).toBeInTheDocument();  // Forks for repo 1
      expect(screen.getByText('8')).toBeInTheDocument();  // Forks for repo 2
    });
  });

  test('displays upcoming milestones', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('Version 1.0')).toBeInTheDocument();
      expect(screen.getByText('First release')).toBeInTheDocument();
    });
  });

  test('shows add repository button', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('Add Repository')).toBeInTheDocument();
    });
  });

  test('opens add repository modal when button is clicked', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      const addButton = screen.getByText('Add Repository');
      fireEvent.click(addButton);
    });

    // Should open modal (assuming AddRepository component renders a modal)
    expect(screen.getByText('Add New Repository')).toBeInTheDocument();
  });

  test('handles API error gracefully', async () => {
    mockedApi.default.get.mockRejectedValue(new Error('API Error'));

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument();
    });
  });

  test('shows empty state when no repositories', async () => {
    mockedApi.default.get.mockImplementation((url: string) => {
      switch (url) {
        case '/api/v1/repositories':
          return Promise.resolve({ data: [] });
        case '/api/v1/milestones':
          return Promise.resolve({ data: [] });
        case '/api/v1/special-files':
          return Promise.resolve({ data: [] });
        default:
          return Promise.reject(new Error('Not found'));
      }
    });

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('0')).toBeInTheDocument(); // Zero repositories
      expect(screen.getByText('No repositories found')).toBeInTheDocument();
    });
  });

  test('navigates to repository detail when repository is clicked', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      const repoLink = screen.getByText('test-repo-1');
      expect(repoLink.closest('a')).toHaveAttribute('href', '/repositories/1');
    });
  });

  test('displays repository status badges', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      // Should show active status for repositories
      const activeElements = screen.getAllByText('Active');
      expect(activeElements.length).toBeGreaterThan(0);
    });
  });

  test('refreshes data when refresh button is clicked', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      const refreshButton = screen.getByLabelText(/refresh/i);
      fireEvent.click(refreshButton);
    });

    // API should be called again
    expect(mockedApi.default.get).toHaveBeenCalledWith('/api/v1/repositories');
  });

  test('filters repositories by search term', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText(/search repositories/i);
      fireEvent.change(searchInput, { target: { value: 'test-repo-1' } });
    });

    // Should filter the displayed repositories
    expect(screen.getByText('test-repo-1')).toBeInTheDocument();
    expect(screen.queryByText('test-repo-2')).not.toBeInTheDocument();
  });

  test('shows correct milestone status', async () => {
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      // Should show pending status for incomplete milestone
      expect(screen.getByText('Pending')).toBeInTheDocument();
    });
  });

  test('handles unauthenticated state', () => {
    const unauthenticatedContext = {
      ...mockAuthContext,
      isAuthenticated: false,
      user: null
    };

    jest.mocked(require('../../../contexts/AuthContext').useAuth).mockReturnValue(unauthenticatedContext);

    renderWithProviders(<Dashboard />);

    // Should not fetch data when not authenticated
    expect(mockedApi.default.get).not.toHaveBeenCalled();
  });
});
