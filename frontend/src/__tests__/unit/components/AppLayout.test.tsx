import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import AppLayout from '../../../components/layout/AppLayout';
import { AuthProvider } from '../../../contexts/AuthContext';

// Mock the AuthContext
const mockAuthContext = {
  user: {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    avatar_url: 'https://example.com/avatar.jpg'
  },
  isAuthenticated: true,
  logout: jest.fn()
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext,
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ConfigProvider>
      <BrowserRouter>
        <AuthProvider>
          {component}
        </AuthProvider>
      </BrowserRouter>
    </ConfigProvider>
  );
};

describe('AppLayout', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders layout with navigation menu', () => {
    renderWithProviders(
      <AppLayout>
        <div>Test Content</div>
      </AppLayout>
    );

    expect(screen.getByText('RepoTimeline')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Repositories')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  test('renders user information when authenticated', () => {
    renderWithProviders(
      <AppLayout>
        <div>Test Content</div>
      </AppLayout>
    );

    expect(screen.getByText('testuser')).toBeInTheDocument();
  });

  test('toggles sidebar collapse', () => {
    renderWithProviders(
      <AppLayout>
        <div>Test Content</div>
      </AppLayout>
    );

    const toggleButton = screen.getByRole('button', { name: /fold/i });
    
    // Initially expanded
    expect(screen.getByText('RepoTimeline')).toBeInTheDocument();
    
    // Click to collapse
    fireEvent.click(toggleButton);
    
    // Should show collapsed version
    expect(screen.getByText('RT')).toBeInTheDocument();
  });

  test('renders minimal layout when not authenticated', () => {
    const unauthenticatedContext = {
      ...mockAuthContext,
      isAuthenticated: false,
      user: null
    };

    jest.mocked(require('../../../contexts/AuthContext').useAuth).mockReturnValue(unauthenticatedContext);

    renderWithProviders(
      <AppLayout>
        <div>Test Content</div>
      </AppLayout>
    );

    expect(screen.queryByText('RepoTimeline')).not.toBeInTheDocument();
    expect(screen.queryByText('Dashboard')).not.toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  test('handles user menu interactions', () => {
    renderWithProviders(
      <AppLayout>
        <div>Test Content</div>
      </AppLayout>
    );

    const userDropdown = screen.getByText('testuser');
    fireEvent.click(userDropdown);

    // Should show user menu options
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Logout')).toBeInTheDocument();
  });

  test('calls logout when logout menu item is clicked', () => {
    renderWithProviders(
      <AppLayout>
        <div>Test Content</div>
      </AppLayout>
    );

    const userDropdown = screen.getByText('testuser');
    fireEvent.click(userDropdown);

    const logoutButton = screen.getByText('Logout');
    fireEvent.click(logoutButton);

    expect(mockAuthContext.logout).toHaveBeenCalledTimes(1);
  });

  test('highlights active menu item based on current route', () => {
    // Mock useLocation to return specific path
    jest.mock('react-router-dom', () => ({
      ...jest.requireActual('react-router-dom'),
      useLocation: () => ({ pathname: '/dashboard' })
    }));

    renderWithProviders(
      <AppLayout>
        <div>Test Content</div>
      </AppLayout>
    );

    const dashboardLink = screen.getByText('Dashboard').closest('li');
    expect(dashboardLink).toHaveClass('ant-menu-item-selected');
  });

  test('renders children content correctly', () => {
    const testContent = (
      <div>
        <h1>Test Page</h1>
        <p>This is test content</p>
      </div>
    );

    renderWithProviders(
      <AppLayout>
        {testContent}
      </AppLayout>
    );

    expect(screen.getByText('Test Page')).toBeInTheDocument();
    expect(screen.getByText('This is test content')).toBeInTheDocument();
  });

  test('handles responsive behavior', () => {
    // Mock window.matchMedia for responsive testing
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(max-width: 768px)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    renderWithProviders(
      <AppLayout>
        <div>Test Content</div>
      </AppLayout>
    );

    // Should render properly on mobile
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });
});
