import axios from 'axios';
import { userService } from '../../services';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('User Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCurrentUser', () => {
    test('fetches current user successfully', async () => {
      const user = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        avatar_url: 'https://example.com/avatar.png'
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: user });
      
      const result = await userService.getCurrentUser();
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/users/me');
      expect(result).toEqual(user);
    });

    test('handles errors correctly', async () => {
      const errorMessage = 'Unauthorized';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(userService.getCurrentUser()).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/users/me');
    });
  });

  describe('updateUserProfile', () => {
    test('updates user profile successfully', async () => {
      const updateData = {
        name: 'Test User',
        bio: 'Software developer'
      };
      
      const updatedUser = {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        name: 'Test User',
        bio: 'Software developer',
        avatar_url: 'https://example.com/avatar.png'
      };
      
      mockedAxios.put.mockResolvedValueOnce({ data: updatedUser });
      
      const result = await userService.updateUserProfile(updateData);
      
      expect(mockedAxios.put).toHaveBeenCalledWith('/api/v1/users/me', updateData);
      expect(result).toEqual(updatedUser);
    });

    test('handles errors correctly', async () => {
      const updateData = {
        name: 'Test User',
        bio: 'Software developer'
      };
      
      const errorMessage = 'Failed to update profile';
      mockedAxios.put.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(userService.updateUserProfile(updateData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.put).toHaveBeenCalledWith('/api/v1/users/me', updateData);
    });
  });

  describe('updateUserSettings', () => {
    test('updates user settings successfully', async () => {
      const settingsData = {
        theme: 'dark',
        notifications_enabled: true
      };
      
      const updatedSettings = {
        id: 1,
        user_id: 1,
        theme: 'dark',
        notifications_enabled: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-02-01T00:00:00Z'
      };
      
      mockedAxios.put.mockResolvedValueOnce({ data: updatedSettings });
      
      const result = await userService.updateUserSettings(settingsData);
      
      expect(mockedAxios.put).toHaveBeenCalledWith('/api/v1/users/me/settings', settingsData);
      expect(result).toEqual(updatedSettings);
    });

    test('handles errors correctly', async () => {
      const settingsData = {
        theme: 'dark',
        notifications_enabled: true
      };
      
      const errorMessage = 'Failed to update settings';
      mockedAxios.put.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(userService.updateUserSettings(settingsData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.put).toHaveBeenCalledWith('/api/v1/users/me/settings', settingsData);
    });
  });

  describe('getUserSettings', () => {
    test('fetches user settings successfully', async () => {
      const settings = {
        id: 1,
        user_id: 1,
        theme: 'light',
        notifications_enabled: false,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: settings });
      
      const result = await userService.getUserSettings();
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/users/me/settings');
      expect(result).toEqual(settings);
    });

    test('handles errors correctly', async () => {
      const errorMessage = 'Failed to fetch settings';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(userService.getUserSettings()).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/users/me/settings');
    });
  });

  describe('addGitHubToken', () => {
    test('adds GitHub token successfully', async () => {
      const tokenData = {
        token: 'ghp_123456789abcdef',
        name: 'RepoTimeline App'
      };
      
      const addedToken = {
        id: 1,
        user_id: 1,
        name: 'RepoTimeline App',
        token_hash: 'hashed_token_value',
        created_at: '2023-02-01T00:00:00Z'
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: addedToken });
      
      const result = await userService.addGitHubToken(tokenData);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/users/me/tokens', tokenData);
      expect(result).toEqual(addedToken);
    });

    test('handles errors correctly', async () => {
      const tokenData = {
        token: 'ghp_123456789abcdef',
        name: 'RepoTimeline App'
      };
      
      const errorMessage = 'Failed to add token';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(userService.addGitHubToken(tokenData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/users/me/tokens', tokenData);
    });
  });

  describe('getGitHubTokens', () => {
    test('fetches GitHub tokens successfully', async () => {
      const tokens = [
        {
          id: 1,
          user_id: 1,
          name: 'RepoTimeline App',
          token_hash: 'hashed_token_value_1',
          created_at: '2023-02-01T00:00:00Z'
        },
        {
          id: 2,
          user_id: 1,
          name: 'Development Token',
          token_hash: 'hashed_token_value_2',
          created_at: '2023-02-15T00:00:00Z'
        }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: tokens });
      
      const result = await userService.getGitHubTokens();
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/users/me/tokens');
      expect(result).toEqual(tokens);
    });

    test('handles errors correctly', async () => {
      const errorMessage = 'Failed to fetch tokens';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(userService.getGitHubTokens()).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/users/me/tokens');
    });
  });

  describe('deleteGitHubToken', () => {
    test('deletes GitHub token successfully', async () => {
      const tokenId = 1;
      
      mockedAxios.delete.mockResolvedValueOnce({ data: {} });
      
      await userService.deleteGitHubToken(tokenId);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/users/me/tokens/${tokenId}`);
    });

    test('handles errors correctly', async () => {
      const tokenId = 1;
      
      const errorMessage = 'Failed to delete token';
      mockedAxios.delete.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(userService.deleteGitHubToken(tokenId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/users/me/tokens/${tokenId}`);
    });
  });

  describe('getUserActivity', () => {
    test('fetches user activity successfully', async () => {
      const activity = {
        repositories_count: 10,
        commits_count: 150,
        milestones_count: 25,
        recent_activity: [
          { date: '2023-02-01', count: 5 },
          { date: '2023-02-02', count: 3 },
          { date: '2023-02-03', count: 7 }
        ]
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: activity });
      
      const result = await userService.getUserActivity();
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/users/me/activity');
      expect(result).toEqual(activity);
    });

    test('handles errors correctly', async () => {
      const errorMessage = 'Failed to fetch user activity';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(userService.getUserActivity()).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/v1/users/me/activity');
    });
  });

  describe('logout', () => {
    test('logs out user successfully', async () => {
      mockedAxios.post.mockResolvedValueOnce({ data: { message: 'Logged out successfully' } });
      
      const result = await userService.logout();
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/auth/logout');
      expect(result).toEqual({ message: 'Logged out successfully' });
    });

    test('handles errors correctly', async () => {
      const errorMessage = 'Failed to logout';
      
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(userService.logout()).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/auth/logout');
    });
  });
}); 