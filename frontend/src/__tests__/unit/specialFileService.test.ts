import axios from 'axios';
import { specialFileService } from '../../services';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Special File Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getSpecialFilesByRepositoryId', () => {
    test('fetches special files successfully', async () => {
      const repositoryId = 1;
      const specialFiles = [
        { id: 1, name: 'README.md', type: 'readme', content: '# Project' },
        { id: 2, name: 'LICENSE', type: 'license', content: 'MIT License' }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: specialFiles });
      
      const result = await specialFileService.getSpecialFilesByRepositoryId(repositoryId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/special-files`, {
        params: { include_deleted: false }
      });
      expect(result).toEqual(specialFiles);
    });

    test('includes deleted special files when specified', async () => {
      const repositoryId = 1;
      const specialFiles = [
        { id: 1, name: 'README.md', type: 'readme', content: '# Project', is_deleted: false },
        { id: 2, name: 'LICENSE', type: 'license', content: 'MIT License', is_deleted: true }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: specialFiles });
      
      const result = await specialFileService.getSpecialFilesByRepositoryId(repositoryId, true);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/special-files`, {
        params: { include_deleted: true }
      });
      expect(result).toEqual(specialFiles);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      const errorMessage = 'Network Error';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(specialFileService.getSpecialFilesByRepositoryId(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/special-files`, {
        params: { include_deleted: false }
      });
    });
  });

  describe('getSpecialFilesByType', () => {
    test('fetches special files by type successfully', async () => {
      const fileType = 'readme';
      const specialFiles = [
        { id: 1, name: 'README.md', type: 'readme', content: '# Project 1' },
        { id: 3, name: 'README.md', type: 'readme', content: '# Project 2' }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: specialFiles });
      
      const result = await specialFileService.getSpecialFilesByType(fileType);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/special-files`, {
        params: { type: fileType, include_deleted: false }
      });
      expect(result).toEqual(specialFiles);
    });

    test('includes deleted special files when specified', async () => {
      const fileType = 'readme';
      const specialFiles = [
        { id: 1, name: 'README.md', type: 'readme', content: '# Project 1', is_deleted: false },
        { id: 3, name: 'README.md', type: 'readme', content: '# Project 2', is_deleted: true }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: specialFiles });
      
      const result = await specialFileService.getSpecialFilesByType(fileType, true);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/special-files`, {
        params: { type: fileType, include_deleted: true }
      });
      expect(result).toEqual(specialFiles);
    });

    test('handles errors correctly', async () => {
      const fileType = 'readme';
      const errorMessage = 'Network Error';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(specialFileService.getSpecialFilesByType(fileType)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/special-files`, {
        params: { type: fileType, include_deleted: false }
      });
    });
  });

  describe('getSpecialFileById', () => {
    test('fetches a special file by ID successfully', async () => {
      const fileId = 1;
      const specialFile = { 
        id: fileId, 
        name: 'README.md', 
        type: 'readme',
        content: '# Project',
        repository_id: 1
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: specialFile });
      
      const result = await specialFileService.getSpecialFileById(fileId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}`, {
        params: { include_deleted: false }
      });
      expect(result).toEqual(specialFile);
    });

    test('includes deleted special file when specified', async () => {
      const fileId = 1;
      const specialFile = { 
        id: fileId, 
        name: 'README.md', 
        type: 'readme',
        content: '# Project',
        repository_id: 1,
        is_deleted: true
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: specialFile });
      
      const result = await specialFileService.getSpecialFileById(fileId, true);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}`, {
        params: { include_deleted: true }
      });
      expect(result).toEqual(specialFile);
    });

    test('handles errors correctly', async () => {
      const fileId = 999;
      const errorMessage = 'Special file not found';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(specialFileService.getSpecialFileById(fileId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}`, {
        params: { include_deleted: false }
      });
    });
  });

  describe('createSpecialFile', () => {
    test('creates a special file successfully', async () => {
      const fileData = {
        repository_id: 1,
        name: 'CONTRIBUTING.md',
        type: 'contributing',
        content: '# How to contribute'
      };
      
      const createdFile = {
        id: 3,
        ...fileData,
        created_at: '2023-02-01T00:00:00Z'
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: createdFile });
      
      const result = await specialFileService.createSpecialFile(fileData);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/special-files', fileData);
      expect(result).toEqual(createdFile);
    });

    test('handles errors correctly', async () => {
      const fileData = {
        repository_id: 1,
        name: 'CONTRIBUTING.md',
        type: 'contributing',
        content: '# How to contribute'
      };
      
      const errorMessage = 'Failed to create special file';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(specialFileService.createSpecialFile(fileData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/special-files', fileData);
    });
  });

  describe('updateSpecialFile', () => {
    test('updates a special file successfully', async () => {
      const fileId = 1;
      const updateData = {
        content: '# Updated Project'
      };
      
      const updatedFile = {
        id: fileId,
        name: 'README.md',
        type: 'readme',
        content: '# Updated Project',
        repository_id: 1,
        updated_at: '2023-02-15T00:00:00Z'
      };
      
      mockedAxios.put.mockResolvedValueOnce({ data: updatedFile });
      
      const result = await specialFileService.updateSpecialFile(fileId, updateData);
      
      expect(mockedAxios.put).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}`, updateData);
      expect(result).toEqual(updatedFile);
    });

    test('handles errors correctly', async () => {
      const fileId = 1;
      const updateData = {
        content: '# Updated Project'
      };
      
      const errorMessage = 'Failed to update special file';
      mockedAxios.put.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(specialFileService.updateSpecialFile(fileId, updateData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.put).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}`, updateData);
    });
  });

  describe('deleteSpecialFile', () => {
    test('soft-deletes a special file successfully', async () => {
      const fileId = 1;
      
      mockedAxios.delete.mockResolvedValueOnce({ data: {} });
      
      await specialFileService.deleteSpecialFile(fileId);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}`, {
        params: { permanent: false }
      });
    });

    test('permanently deletes a special file when specified', async () => {
      const fileId = 1;
      
      mockedAxios.delete.mockResolvedValueOnce({ data: {} });
      
      await specialFileService.deleteSpecialFile(fileId, true);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}`, {
        params: { permanent: true }
      });
    });

    test('handles errors correctly', async () => {
      const fileId = 1;
      
      const errorMessage = 'Failed to delete special file';
      mockedAxios.delete.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(specialFileService.deleteSpecialFile(fileId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}`, {
        params: { permanent: false }
      });
    });
  });

  describe('restoreSpecialFile', () => {
    test('restores a special file successfully', async () => {
      const fileId = 1;
      const restoredFile = {
        id: fileId,
        name: 'README.md',
        type: 'readme',
        content: '# Project',
        repository_id: 1,
        is_deleted: false,
        deleted_at: null
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: restoredFile });
      
      const result = await specialFileService.restoreSpecialFile(fileId);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}/restore`);
      expect(result).toEqual(restoredFile);
    });

    test('handles errors correctly', async () => {
      const fileId = 1;
      
      const errorMessage = 'Failed to restore special file';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(specialFileService.restoreSpecialFile(fileId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/special-files/${fileId}/restore`);
    });
  });

  describe('getSpecialFileTypes', () => {
    test('fetches special file types successfully', async () => {
      const repositoryId = 1;
      const fileTypes = [
        { type: 'readme', count: 1 },
        { type: 'license', count: 1 },
        { type: 'contributing', count: 1 }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: fileTypes });
      
      const result = await specialFileService.getSpecialFileTypes(repositoryId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/analytics/special-files`);
      expect(result).toEqual(fileTypes);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      
      const errorMessage = 'Failed to fetch special file types';
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(specialFileService.getSpecialFileTypes(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/analytics/special-files`);
    });
  });

  describe('syncSpecialFiles', () => {
    test('syncs special files successfully', async () => {
      const repositoryId = 1;
      const syncedFiles = [
        { id: 1, name: 'README.md', type: 'readme', content: '# Project' },
        { id: 2, name: 'LICENSE', type: 'license', content: 'MIT License' },
        { id: 3, name: 'CONTRIBUTING.md', type: 'contributing', content: '# How to contribute' }
      ];
      
      mockedAxios.post.mockResolvedValueOnce({ data: syncedFiles });
      
      const result = await specialFileService.syncSpecialFiles(repositoryId);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/sync/special-files`);
      expect(result).toEqual(syncedFiles);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      
      const errorMessage = 'Failed to sync special files';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(specialFileService.syncSpecialFiles(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/sync/special-files`);
    });
  });
}); 