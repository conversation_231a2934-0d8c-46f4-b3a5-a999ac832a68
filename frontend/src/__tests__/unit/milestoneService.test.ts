import axios from 'axios';
import { milestoneService } from '../../services';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Milestone Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getMilestonesByRepositoryId', () => {
    test('fetches milestones successfully', async () => {
      const repositoryId = 1;
      const milestones = [
        { id: 1, title: 'v1.0.0', description: 'First release' },
        { id: 2, title: 'v1.1.0', description: 'Feature update' }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: milestones });
      
      const result = await milestoneService.getMilestonesByRepositoryId(repositoryId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/milestones`, {
        params: { include_deleted: false }
      });
      expect(result).toEqual(milestones);
    });

    test('includes deleted milestones when specified', async () => {
      const repositoryId = 1;
      const milestones = [
        { id: 1, title: 'v1.0.0', description: 'First release', is_deleted: false },
        { id: 2, title: 'v1.1.0', description: 'Feature update', is_deleted: true }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: milestones });
      
      const result = await milestoneService.getMilestonesByRepositoryId(repositoryId, true);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/milestones`, {
        params: { include_deleted: true }
      });
      expect(result).toEqual(milestones);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      const errorMessage = 'Network Error';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.getMilestonesByRepositoryId(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/milestones`, {
        params: { include_deleted: false }
      });
    });
  });

  describe('getMilestoneById', () => {
    test('fetches a milestone by ID successfully', async () => {
      const milestoneId = 1;
      const milestone = { 
        id: milestoneId, 
        title: 'v1.0.0', 
        description: 'First release',
        due_date: '2023-03-01T00:00:00Z',
        is_completed: false
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: milestone });
      
      const result = await milestoneService.getMilestoneById(milestoneId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}`, {
        params: { include_deleted: false }
      });
      expect(result).toEqual(milestone);
    });

    test('includes deleted milestone when specified', async () => {
      const milestoneId = 1;
      const milestone = { 
        id: milestoneId, 
        title: 'v1.0.0', 
        description: 'First release',
        due_date: '2023-03-01T00:00:00Z',
        is_completed: false,
        is_deleted: true
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: milestone });
      
      const result = await milestoneService.getMilestoneById(milestoneId, true);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}`, {
        params: { include_deleted: true }
      });
      expect(result).toEqual(milestone);
    });

    test('handles errors correctly', async () => {
      const milestoneId = 999;
      const errorMessage = 'Milestone not found';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.getMilestoneById(milestoneId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}`, {
        params: { include_deleted: false }
      });
    });
  });

  describe('createMilestone', () => {
    test('creates a milestone successfully', async () => {
      const milestoneData = {
        repository_id: 1,
        title: 'v1.2.0',
        description: 'Bug fixes and improvements',
        due_date: '2023-04-01T00:00:00Z'
      };
      
      const createdMilestone = {
        id: 3,
        ...milestoneData,
        is_completed: false,
        created_at: '2023-02-01T00:00:00Z'
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: createdMilestone });
      
      const result = await milestoneService.createMilestone(milestoneData);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/milestones', milestoneData);
      expect(result).toEqual(createdMilestone);
    });

    test('handles errors correctly', async () => {
      const milestoneData = {
        repository_id: 1,
        title: 'v1.2.0',
        description: 'Bug fixes and improvements',
        due_date: '2023-04-01T00:00:00Z'
      };
      
      const errorMessage = 'Failed to create milestone';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.createMilestone(milestoneData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/milestones', milestoneData);
    });
  });

  describe('updateMilestone', () => {
    test('updates a milestone successfully', async () => {
      const milestoneId = 1;
      const updateData = {
        description: 'Updated milestone description'
      };
      
      const updatedMilestone = {
        id: milestoneId,
        title: 'v1.0.0',
        description: 'Updated milestone description',
        due_date: '2023-03-01T00:00:00Z',
        is_completed: false,
        updated_at: '2023-02-15T00:00:00Z'
      };
      
      mockedAxios.put.mockResolvedValueOnce({ data: updatedMilestone });
      
      const result = await milestoneService.updateMilestone(milestoneId, updateData);
      
      expect(mockedAxios.put).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}`, updateData);
      expect(result).toEqual(updatedMilestone);
    });

    test('handles errors correctly', async () => {
      const milestoneId = 1;
      const updateData = {
        description: 'Updated milestone description'
      };
      
      const errorMessage = 'Failed to update milestone';
      mockedAxios.put.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.updateMilestone(milestoneId, updateData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.put).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}`, updateData);
    });
  });

  describe('completeMilestone', () => {
    test('completes a milestone successfully', async () => {
      const milestoneId = 1;
      const completedMilestone = {
        id: milestoneId,
        title: 'v1.0.0',
        description: 'First release',
        due_date: '2023-03-01T00:00:00Z',
        is_completed: true,
        completed_at: '2023-02-20T00:00:00Z'
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: completedMilestone });
      
      const result = await milestoneService.completeMilestone(milestoneId);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}/complete`);
      expect(result).toEqual(completedMilestone);
    });

    test('handles errors correctly', async () => {
      const milestoneId = 1;
      
      const errorMessage = 'Failed to complete milestone';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.completeMilestone(milestoneId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}/complete`);
    });
  });

  describe('reopenMilestone', () => {
    test('reopens a milestone successfully', async () => {
      const milestoneId = 1;
      const reopenedMilestone = {
        id: milestoneId,
        title: 'v1.0.0',
        description: 'First release',
        due_date: '2023-03-01T00:00:00Z',
        is_completed: false,
        completed_at: null
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: reopenedMilestone });
      
      const result = await milestoneService.reopenMilestone(milestoneId);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}/reopen`);
      expect(result).toEqual(reopenedMilestone);
    });

    test('handles errors correctly', async () => {
      const milestoneId = 1;
      
      const errorMessage = 'Failed to reopen milestone';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.reopenMilestone(milestoneId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}/reopen`);
    });
  });

  describe('deleteMilestone', () => {
    test('soft-deletes a milestone successfully', async () => {
      const milestoneId = 1;
      
      mockedAxios.delete.mockResolvedValueOnce({ data: {} });
      
      await milestoneService.deleteMilestone(milestoneId);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}`, {
        params: { permanent: false }
      });
    });

    test('permanently deletes a milestone when specified', async () => {
      const milestoneId = 1;
      
      mockedAxios.delete.mockResolvedValueOnce({ data: {} });
      
      await milestoneService.deleteMilestone(milestoneId, true);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}`, {
        params: { permanent: true }
      });
    });

    test('handles errors correctly', async () => {
      const milestoneId = 1;
      
      const errorMessage = 'Failed to delete milestone';
      mockedAxios.delete.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.deleteMilestone(milestoneId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}`, {
        params: { permanent: false }
      });
    });
  });

  describe('restoreMilestone', () => {
    test('restores a milestone successfully', async () => {
      const milestoneId = 1;
      const restoredMilestone = {
        id: milestoneId,
        title: 'v1.0.0',
        description: 'First release',
        due_date: '2023-03-01T00:00:00Z',
        is_completed: false,
        is_deleted: false,
        deleted_at: null
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: restoredMilestone });
      
      const result = await milestoneService.restoreMilestone(milestoneId);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}/restore`);
      expect(result).toEqual(restoredMilestone);
    });

    test('handles errors correctly', async () => {
      const milestoneId = 1;
      
      const errorMessage = 'Failed to restore milestone';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.restoreMilestone(milestoneId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/milestones/${milestoneId}/restore`);
    });
  });

  describe('getMilestoneStatus', () => {
    test('fetches milestone status successfully', async () => {
      const repositoryId = 1;
      const statusData = {
        completed: 5,
        open: 3,
        total: 8
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: statusData });
      
      const result = await milestoneService.getMilestoneStatus(repositoryId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/analytics/milestones`);
      expect(result).toEqual(statusData);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      
      const errorMessage = 'Failed to fetch milestone status';
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.getMilestoneStatus(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/analytics/milestones`);
    });
  });

  describe('syncMilestones', () => {
    test('syncs milestones successfully', async () => {
      const repositoryId = 1;
      const syncedMilestones = [
        { id: 1, title: 'v1.0.0', description: 'First release' },
        { id: 2, title: 'v1.1.0', description: 'Feature update' },
        { id: 3, title: 'v1.2.0', description: 'Bug fixes and improvements' }
      ];
      
      mockedAxios.post.mockResolvedValueOnce({ data: syncedMilestones });
      
      const result = await milestoneService.syncMilestones(repositoryId);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/sync/milestones`);
      expect(result).toEqual(syncedMilestones);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      
      const errorMessage = 'Failed to sync milestones';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(milestoneService.syncMilestones(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/sync/milestones`);
    });
  });
}); 