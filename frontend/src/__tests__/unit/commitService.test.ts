import axios from 'axios';
import { commitService } from '../../services';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Commit Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCommitsByRepositoryId', () => {
    test('fetches commits successfully', async () => {
      const repositoryId = 1;
      const commits = [
        { id: 1, message: 'Initial commit', sha: 'abc123' },
        { id: 2, message: 'Update README', sha: 'def456' }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: commits });
      
      const result = await commitService.getCommitsByRepositoryId(repositoryId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/commits`, {
        params: { include_deleted: false }
      });
      expect(result).toEqual(commits);
    });

    test('includes deleted commits when specified', async () => {
      const repositoryId = 1;
      const commits = [
        { id: 1, message: 'Initial commit', sha: 'abc123', is_deleted: false },
        { id: 2, message: 'Update README', sha: 'def456', is_deleted: true }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: commits });
      
      const result = await commitService.getCommitsByRepositoryId(repositoryId, true);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/commits`, {
        params: { include_deleted: true }
      });
      expect(result).toEqual(commits);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      const errorMessage = 'Network Error';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(commitService.getCommitsByRepositoryId(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/commits`, {
        params: { include_deleted: false }
      });
    });
  });

  describe('getCommitById', () => {
    test('fetches a commit by ID successfully', async () => {
      const commitId = 1;
      const commit = { 
        id: commitId, 
        message: 'Initial commit', 
        sha: 'abc123',
        author: 'John Doe',
        date: '2023-01-01T00:00:00Z'
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: commit });
      
      const result = await commitService.getCommitById(commitId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/commits/${commitId}`, {
        params: { include_deleted: false }
      });
      expect(result).toEqual(commit);
    });

    test('includes deleted commit when specified', async () => {
      const commitId = 1;
      const commit = { 
        id: commitId, 
        message: 'Initial commit', 
        sha: 'abc123',
        author: 'John Doe',
        date: '2023-01-01T00:00:00Z',
        is_deleted: true
      };
      
      mockedAxios.get.mockResolvedValueOnce({ data: commit });
      
      const result = await commitService.getCommitById(commitId, true);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/commits/${commitId}`, {
        params: { include_deleted: true }
      });
      expect(result).toEqual(commit);
    });

    test('handles errors correctly', async () => {
      const commitId = 999;
      const errorMessage = 'Commit not found';
      
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(commitService.getCommitById(commitId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/commits/${commitId}`, {
        params: { include_deleted: false }
      });
    });
  });

  describe('createCommit', () => {
    test('creates a commit successfully', async () => {
      const commitData = {
        repository_id: 1,
        message: 'Add new feature',
        sha: 'ghi789',
        author: 'Jane Smith',
        date: '2023-01-02T00:00:00Z'
      };
      
      const createdCommit = {
        id: 3,
        ...commitData,
        created_at: '2023-01-02T00:00:00Z'
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: createdCommit });
      
      const result = await commitService.createCommit(commitData);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/commits', commitData);
      expect(result).toEqual(createdCommit);
    });

    test('handles errors correctly', async () => {
      const commitData = {
        repository_id: 1,
        message: 'Add new feature',
        sha: 'ghi789',
        author: 'Jane Smith',
        date: '2023-01-02T00:00:00Z'
      };
      
      const errorMessage = 'Failed to create commit';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(commitService.createCommit(commitData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/commits', commitData);
    });
  });

  describe('updateCommit', () => {
    test('updates a commit successfully', async () => {
      const commitId = 1;
      const updateData = {
        message: 'Updated commit message'
      };
      
      const updatedCommit = {
        id: commitId,
        message: 'Updated commit message',
        sha: 'abc123',
        author: 'John Doe',
        date: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-03T00:00:00Z'
      };
      
      mockedAxios.put.mockResolvedValueOnce({ data: updatedCommit });
      
      const result = await commitService.updateCommit(commitId, updateData);
      
      expect(mockedAxios.put).toHaveBeenCalledWith(`/api/v1/commits/${commitId}`, updateData);
      expect(result).toEqual(updatedCommit);
    });

    test('handles errors correctly', async () => {
      const commitId = 1;
      const updateData = {
        message: 'Updated commit message'
      };
      
      const errorMessage = 'Failed to update commit';
      mockedAxios.put.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(commitService.updateCommit(commitId, updateData)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.put).toHaveBeenCalledWith(`/api/v1/commits/${commitId}`, updateData);
    });
  });

  describe('deleteCommit', () => {
    test('soft-deletes a commit successfully', async () => {
      const commitId = 1;
      
      mockedAxios.delete.mockResolvedValueOnce({ data: {} });
      
      await commitService.deleteCommit(commitId);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/commits/${commitId}`, {
        params: { permanent: false }
      });
    });

    test('permanently deletes a commit when specified', async () => {
      const commitId = 1;
      
      mockedAxios.delete.mockResolvedValueOnce({ data: {} });
      
      await commitService.deleteCommit(commitId, true);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/commits/${commitId}`, {
        params: { permanent: true }
      });
    });

    test('handles errors correctly', async () => {
      const commitId = 1;
      
      const errorMessage = 'Failed to delete commit';
      mockedAxios.delete.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(commitService.deleteCommit(commitId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.delete).toHaveBeenCalledWith(`/api/v1/commits/${commitId}`, {
        params: { permanent: false }
      });
    });
  });

  describe('restoreCommit', () => {
    test('restores a commit successfully', async () => {
      const commitId = 1;
      const restoredCommit = {
        id: commitId,
        message: 'Initial commit',
        sha: 'abc123',
        author: 'John Doe',
        date: '2023-01-01T00:00:00Z',
        is_deleted: false,
        deleted_at: null
      };
      
      mockedAxios.post.mockResolvedValueOnce({ data: restoredCommit });
      
      const result = await commitService.restoreCommit(commitId);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/commits/${commitId}/restore`);
      expect(result).toEqual(restoredCommit);
    });

    test('handles errors correctly', async () => {
      const commitId = 1;
      
      const errorMessage = 'Failed to restore commit';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(commitService.restoreCommit(commitId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/commits/${commitId}/restore`);
    });
  });

  describe('getCommitActivity', () => {
    test('fetches commit activity successfully', async () => {
      const repositoryId = 1;
      const activityData = [
        { date: '2023-01-01', count: 5 },
        { date: '2023-01-02', count: 3 },
        { date: '2023-01-03', count: 7 }
      ];
      
      mockedAxios.get.mockResolvedValueOnce({ data: activityData });
      
      const result = await commitService.getCommitActivity(repositoryId);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/analytics/commits`);
      expect(result).toEqual(activityData);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      
      const errorMessage = 'Failed to fetch commit activity';
      mockedAxios.get.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(commitService.getCommitActivity(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.get).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/analytics/commits`);
    });
  });

  describe('syncCommits', () => {
    test('syncs commits successfully', async () => {
      const repositoryId = 1;
      const syncedCommits = [
        { id: 1, message: 'Initial commit', sha: 'abc123' },
        { id: 2, message: 'Update README', sha: 'def456' },
        { id: 3, message: 'Add new feature', sha: 'ghi789' }
      ];
      
      mockedAxios.post.mockResolvedValueOnce({ data: syncedCommits });
      
      const result = await commitService.syncCommits(repositoryId);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/sync/commits`);
      expect(result).toEqual(syncedCommits);
    });

    test('handles errors correctly', async () => {
      const repositoryId = 1;
      
      const errorMessage = 'Failed to sync commits';
      mockedAxios.post.mockRejectedValueOnce(new Error(errorMessage));
      
      await expect(commitService.syncCommits(repositoryId)).rejects.toThrow(errorMessage);
      
      expect(mockedAxios.post).toHaveBeenCalledWith(`/api/v1/repositories/${repositoryId}/sync/commits`);
    });
  });
}); 