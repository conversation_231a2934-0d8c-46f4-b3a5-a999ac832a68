import React from 'react';
import { render, screen, waitFor, fireEvent, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import App from '../../App';
import * as api from '../../services/api';

// Mock the API service
jest.mock('../../services/api');
const mockedApi = api as jest.Mocked<typeof api>;

// Mock the AuthContext with authenticated user
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>'
    },
    isAuthenticated: true,
    logout: jest.fn(),
    login: jest.fn()
  }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const renderApp = () => {
  return render(
    <ConfigProvider>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </ConfigProvider>
  );
};

const mockRepository = {
  id: 1,
  name: 'test-repo',
  full_name: 'user/test-repo',
  description: 'Test repository',
  stars: 10,
  forks: 5,
  open_issues: 2,
  is_active: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-02T00:00:00Z',
  special_files: [],
  commits: [],
  milestones: []
};

describe('Repository Workflow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default API responses
    mockedApi.default.get.mockImplementation((url: string) => {
      switch (url) {
        case '/api/v1/repositories':
          return Promise.resolve({ data: [] });
        case '/api/v1/milestones':
          return Promise.resolve({ data: [] });
        case '/api/v1/special-files':
          return Promise.resolve({ data: [] });
        default:
          return Promise.reject(new Error('Not found'));
      }
    });

    mockedApi.default.post.mockResolvedValue({ data: mockRepository });
  });

  test('complete repository management workflow', async () => {
    const user = userEvent.setup();
    
    // 1. Start at dashboard
    renderApp();

    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });

    // 2. Click Add Repository button
    const addRepoButton = await screen.findByText('Add Repository');
    await user.click(addRepoButton);

    // 3. Fill in repository form
    await waitFor(() => {
      expect(screen.getByText('Add New Repository')).toBeInTheDocument();
    });

    const repoInput = screen.getByPlaceholderText(/repository name/i);
    await user.type(repoInput, 'user/test-repo');

    // 4. Submit form
    const submitButton = screen.getByText('Add Repository');
    await user.click(submitButton);

    // 5. Verify API call was made
    await waitFor(() => {
      expect(mockedApi.default.post).toHaveBeenCalledWith('/api/v1/repositories', {
        full_name: 'user/test-repo'
      });
    });

    // 6. Verify success message
    await waitFor(() => {
      expect(screen.getByText(/repository added successfully/i)).toBeInTheDocument();
    });

    // 7. Verify repository appears in list
    mockedApi.default.get.mockImplementation((url: string) => {
      if (url === '/api/v1/repositories') {
        return Promise.resolve({ data: [mockRepository] });
      }
      return Promise.resolve({ data: [] });
    });

    // Refresh the page or trigger re-fetch
    const refreshButton = screen.getByLabelText(/refresh/i);
    await user.click(refreshButton);

    await waitFor(() => {
      expect(screen.getByText('test-repo')).toBeInTheDocument();
    });
  });

  test('repository detail navigation and management', async () => {
    const user = userEvent.setup();

    // Setup repository detail API response
    mockedApi.default.get.mockImplementation((url: string) => {
      if (url === '/api/v1/repositories') {
        return Promise.resolve({ data: [mockRepository] });
      }
      if (url === '/api/v1/repositories/1') {
        return Promise.resolve({ data: mockRepository });
      }
      return Promise.resolve({ data: [] });
    });

    renderApp();

    // Wait for dashboard to load
    await waitFor(() => {
      expect(screen.getByText('test-repo')).toBeInTheDocument();
    });

    // Click on repository to navigate to detail page
    const repoLink = screen.getByText('test-repo');
    await user.click(repoLink);

    // Verify navigation to repository detail
    await waitFor(() => {
      expect(screen.getByText('Repository Details')).toBeInTheDocument();
      expect(screen.getByText('user/test-repo')).toBeInTheDocument();
    });

    // Test tab navigation
    const specialFilesTab = screen.getByText('Special Files');
    await user.click(specialFilesTab);

    await waitFor(() => {
      expect(screen.getByText('No special files found')).toBeInTheDocument();
    });

    const commitsTab = screen.getByText('Commits');
    await user.click(commitsTab);

    await waitFor(() => {
      expect(screen.getByText('No commits found')).toBeInTheDocument();
    });

    const milestonesTab = screen.getByText('Milestones');
    await user.click(milestonesTab);

    await waitFor(() => {
      expect(screen.getByText('No milestones found')).toBeInTheDocument();
    });
  });

  test('milestone creation and management workflow', async () => {
    const user = userEvent.setup();

    const mockMilestone = {
      id: 1,
      repository_id: 1,
      title: 'Version 1.0',
      description: 'First release',
      due_date: '2024-12-31T23:59:59Z',
      is_completed: false,
      created_at: '2023-01-01T00:00:00Z'
    };

    // Setup API responses
    mockedApi.default.get.mockImplementation((url: string) => {
      if (url === '/api/v1/repositories/1') {
        return Promise.resolve({ 
          data: { 
            ...mockRepository, 
            milestones: [mockMilestone] 
          } 
        });
      }
      return Promise.resolve({ data: [] });
    });

    mockedApi.default.post.mockResolvedValue({ data: mockMilestone });

    renderApp();

    // Navigate to repository detail
    window.history.pushState({}, 'Repository Detail', '/repositories/1');

    await waitFor(() => {
      expect(screen.getByText('Repository Details')).toBeInTheDocument();
    });

    // Go to milestones tab
    const milestonesTab = screen.getByText('Milestones');
    await user.click(milestonesTab);

    // Click Add Milestone button
    const addMilestoneButton = screen.getByText('Add Milestone');
    await user.click(addMilestoneButton);

    // Fill milestone form
    await waitFor(() => {
      expect(screen.getByText('Create Milestone')).toBeInTheDocument();
    });

    const titleInput = screen.getByPlaceholderText(/milestone title/i);
    await user.type(titleInput, 'Version 1.0');

    const descriptionInput = screen.getByPlaceholderText(/description/i);
    await user.type(descriptionInput, 'First release');

    // Submit milestone
    const submitButton = screen.getByText('Create Milestone');
    await user.click(submitButton);

    // Verify API call
    await waitFor(() => {
      expect(mockedApi.default.post).toHaveBeenCalledWith('/api/v1/milestones', {
        repository_id: 1,
        title: 'Version 1.0',
        description: 'First release',
        due_date: expect.any(String)
      });
    });

    // Verify milestone appears in list
    await waitFor(() => {
      expect(screen.getByText('Version 1.0')).toBeInTheDocument();
      expect(screen.getByText('First release')).toBeInTheDocument();
    });
  });

  test('special files sync workflow', async () => {
    const user = userEvent.setup();

    const mockSpecialFiles = [
      {
        id: 1,
        repository_id: 1,
        file_type: 'dockerwrapper',
        file_path: '.dockerwrapper',
        content: 'wrapper content',
        created_at: '2023-01-01T00:00:00Z'
      },
      {
        id: 2,
        repository_id: 1,
        file_type: 'claude',
        file_path: '.claude',
        content: 'claude config',
        created_at: '2023-01-01T00:00:00Z'
      }
    ];

    // Setup API responses
    mockedApi.default.get.mockImplementation((url: string) => {
      if (url === '/api/v1/repositories/1') {
        return Promise.resolve({ 
          data: { 
            ...mockRepository, 
            special_files: mockSpecialFiles 
          } 
        });
      }
      return Promise.resolve({ data: [] });
    });

    mockedApi.default.post.mockResolvedValue({ data: mockSpecialFiles });

    renderApp();

    // Navigate to repository detail
    window.history.pushState({}, 'Repository Detail', '/repositories/1');

    await waitFor(() => {
      expect(screen.getByText('Repository Details')).toBeInTheDocument();
    });

    // Go to special files tab
    const specialFilesTab = screen.getByText('Special Files');
    await user.click(specialFilesTab);

    // Click sync button
    const syncButton = screen.getByText('Sync Files');
    await user.click(syncButton);

    // Verify API call
    await waitFor(() => {
      expect(mockedApi.default.post).toHaveBeenCalledWith('/api/v1/special-files/sync/1');
    });

    // Verify files appear in list
    await waitFor(() => {
      expect(screen.getByText('.dockerwrapper')).toBeInTheDocument();
      expect(screen.getByText('.claude')).toBeInTheDocument();
    });

    // Test file content viewing
    const dockerwrapperFile = screen.getByText('.dockerwrapper');
    await user.click(dockerwrapperFile);

    await waitFor(() => {
      expect(screen.getByText('wrapper content')).toBeInTheDocument();
    });
  });

  test('repository deletion and restoration workflow', async () => {
    const user = userEvent.setup();

    // Setup API responses
    mockedApi.default.get.mockImplementation((url: string) => {
      if (url === '/api/v1/repositories') {
        return Promise.resolve({ data: [mockRepository] });
      }
      if (url === '/api/v1/repositories/deleted') {
        return Promise.resolve({ data: [] });
      }
      return Promise.resolve({ data: [] });
    });

    mockedApi.default.delete.mockResolvedValue({ data: {} });
    mockedApi.default.post.mockResolvedValue({ data: mockRepository });

    renderApp();

    // Wait for repository to load
    await waitFor(() => {
      expect(screen.getByText('test-repo')).toBeInTheDocument();
    });

    // Find and click delete button
    const deleteButton = screen.getByLabelText(/delete/i);
    await user.click(deleteButton);

    // Confirm deletion
    await waitFor(() => {
      expect(screen.getByText('Are you sure?')).toBeInTheDocument();
    });

    const confirmButton = screen.getByText('Yes, delete');
    await user.click(confirmButton);

    // Verify API call
    await waitFor(() => {
      expect(mockedApi.default.delete).toHaveBeenCalledWith('/api/v1/repositories/1');
    });

    // Navigate to trash
    const trashLink = screen.getByText('Trash');
    await user.click(trashLink);

    // Setup trash API response
    mockedApi.default.get.mockImplementation((url: string) => {
      if (url === '/api/v1/repositories/deleted') {
        return Promise.resolve({ data: [{ ...mockRepository, is_deleted: true }] });
      }
      return Promise.resolve({ data: [] });
    });

    // Verify repository appears in trash
    await waitFor(() => {
      expect(screen.getByText('test-repo')).toBeInTheDocument();
    });

    // Restore repository
    const restoreButton = screen.getByText('Restore');
    await user.click(restoreButton);

    // Verify API call
    await waitFor(() => {
      expect(mockedApi.default.post).toHaveBeenCalledWith('/api/v1/repositories/1/restore');
    });
  });

  test('error handling throughout workflow', async () => {
    const user = userEvent.setup();

    // Setup API to return errors
    mockedApi.default.post.mockRejectedValue(new Error('API Error'));

    renderApp();

    // Try to add repository
    const addRepoButton = await screen.findByText('Add Repository');
    await user.click(addRepoButton);

    const repoInput = screen.getByPlaceholderText(/repository name/i);
    await user.type(repoInput, 'user/test-repo');

    const submitButton = screen.getByText('Add Repository');
    await user.click(submitButton);

    // Verify error message is displayed
    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument();
    });

    // Verify form is still accessible for retry
    expect(repoInput).toBeInTheDocument();
    expect(submitButton).toBeInTheDocument();
  });
});
