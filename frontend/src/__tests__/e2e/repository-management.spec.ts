import { test, expect } from '@playwright/test';

// Mock API responses
const mockRepository = {
  id: 1,
  name: 'test-repo',
  full_name: 'user/test-repo',
  description: 'Test repository for E2E testing',
  stars: 15,
  forks: 3,
  open_issues: 2,
  is_active: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-02T00:00:00Z'
};

const mockMilestone = {
  id: 1,
  repository_id: 1,
  title: 'Version 1.0',
  description: 'First major release',
  due_date: '2024-12-31T23:59:59Z',
  is_completed: false,
  created_at: '2023-01-01T00:00:00Z'
};

test.describe('Repository Management E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API endpoints
    await page.route('**/api/v1/repositories', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([mockRepository])
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify(mockRepository)
        });
      }
    });

    await page.route('**/api/v1/milestones', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([mockMilestone])
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify(mockMilestone)
        });
      }
    });

    await page.route('**/api/v1/special-files', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([])
      });
    });

    // Navigate to the application
    await page.goto('/');
  });

  test('should display dashboard with repository statistics', async ({ page }) => {
    // Wait for dashboard to load
    await expect(page.locator('h1')).toContainText('Dashboard');
    
    // Check statistics cards
    await expect(page.locator('[data-testid="total-repositories"]')).toContainText('1');
    await expect(page.locator('[data-testid="total-milestones"]')).toContainText('1');
    
    // Check repository list
    await expect(page.locator('text=test-repo')).toBeVisible();
    await expect(page.locator('text=Test repository for E2E testing')).toBeVisible();
  });

  test('should add a new repository', async ({ page }) => {
    // Click Add Repository button
    await page.click('text=Add Repository');
    
    // Fill in the repository form
    await expect(page.locator('text=Add New Repository')).toBeVisible();
    await page.fill('[placeholder*="repository name"]', 'user/new-test-repo');
    
    // Submit the form
    await page.click('button:has-text("Add Repository")');
    
    // Verify success message
    await expect(page.locator('text=Repository added successfully')).toBeVisible();
    
    // Verify the repository appears in the list
    await expect(page.locator('text=new-test-repo')).toBeVisible();
  });

  test('should navigate to repository detail page', async ({ page }) => {
    // Click on repository name
    await page.click('text=test-repo');
    
    // Verify navigation to detail page
    await expect(page.locator('h1')).toContainText('Repository Details');
    await expect(page.locator('text=user/test-repo')).toBeVisible();
    
    // Check repository information
    await expect(page.locator('text=15')).toBeVisible(); // Stars
    await expect(page.locator('text=3')).toBeVisible();  // Forks
    await expect(page.locator('text=2')).toBeVisible();  // Issues
  });

  test('should manage repository tabs', async ({ page }) => {
    // Navigate to repository detail
    await page.click('text=test-repo');
    
    // Test Special Files tab
    await page.click('text=Special Files');
    await expect(page.locator('text=No special files found')).toBeVisible();
    
    // Test Commits tab
    await page.click('text=Commits');
    await expect(page.locator('text=No commits found')).toBeVisible();
    
    // Test Milestones tab
    await page.click('text=Milestones');
    await expect(page.locator('text=Version 1.0')).toBeVisible();
    await expect(page.locator('text=First major release')).toBeVisible();
  });

  test('should create a new milestone', async ({ page }) => {
    // Navigate to repository detail
    await page.click('text=test-repo');
    
    // Go to Milestones tab
    await page.click('text=Milestones');
    
    // Click Add Milestone
    await page.click('text=Add Milestone');
    
    // Fill milestone form
    await expect(page.locator('text=Create Milestone')).toBeVisible();
    await page.fill('[placeholder*="milestone title"]', 'Version 2.0');
    await page.fill('[placeholder*="description"]', 'Second major release');
    
    // Set due date
    await page.click('[placeholder*="due date"]');
    await page.click('text=31'); // Select day 31
    
    // Submit milestone
    await page.click('button:has-text("Create Milestone")');
    
    // Verify milestone was created
    await expect(page.locator('text=Milestone created successfully')).toBeVisible();
    await expect(page.locator('text=Version 2.0')).toBeVisible();
  });

  test('should complete and reopen milestones', async ({ page }) => {
    // Navigate to repository detail and milestones tab
    await page.click('text=test-repo');
    await page.click('text=Milestones');
    
    // Complete milestone
    await page.click('[data-testid="complete-milestone-1"]');
    await expect(page.locator('text=Milestone completed')).toBeVisible();
    
    // Verify milestone status changed
    await expect(page.locator('[data-testid="milestone-status-1"]')).toContainText('Completed');
    
    // Reopen milestone
    await page.click('[data-testid="reopen-milestone-1"]');
    await expect(page.locator('text=Milestone reopened')).toBeVisible();
    
    // Verify milestone status changed back
    await expect(page.locator('[data-testid="milestone-status-1"]')).toContainText('Pending');
  });

  test('should sync special files', async ({ page }) => {
    // Mock special files response for sync
    await page.route('**/api/v1/special-files/sync/*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 1,
            repository_id: 1,
            file_type: 'dockerwrapper',
            file_path: '.dockerwrapper',
            content: 'wrapper configuration',
            created_at: '2023-01-01T00:00:00Z'
          }
        ])
      });
    });
    
    // Navigate to repository detail and special files tab
    await page.click('text=test-repo');
    await page.click('text=Special Files');
    
    // Click sync button
    await page.click('text=Sync Files');
    
    // Verify sync success
    await expect(page.locator('text=Files synced successfully')).toBeVisible();
    await expect(page.locator('text=.dockerwrapper')).toBeVisible();
  });

  test('should delete and restore repository', async ({ page }) => {
    // Mock delete and restore endpoints
    await page.route('**/api/v1/repositories/1', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ message: 'Repository deleted' })
        });
      }
    });
    
    await page.route('**/api/v1/repositories/1/restore', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockRepository)
      });
    });
    
    await page.route('**/api/v1/repositories/deleted', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([{ ...mockRepository, is_deleted: true }])
      });
    });
    
    // Delete repository
    await page.click('[data-testid="delete-repository-1"]');
    await page.click('text=Yes, delete');
    
    // Verify deletion success
    await expect(page.locator('text=Repository deleted successfully')).toBeVisible();
    
    // Navigate to trash
    await page.click('text=Trash');
    
    // Verify repository appears in trash
    await expect(page.locator('text=test-repo')).toBeVisible();
    
    // Restore repository
    await page.click('[data-testid="restore-repository-1"]');
    
    // Verify restoration success
    await expect(page.locator('text=Repository restored successfully')).toBeVisible();
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/v1/repositories', async route => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ detail: 'Repository already exists' })
        });
      }
    });
    
    // Try to add repository
    await page.click('text=Add Repository');
    await page.fill('[placeholder*="repository name"]', 'user/existing-repo');
    await page.click('button:has-text("Add Repository")');
    
    // Verify error message is displayed
    await expect(page.locator('text=Repository already exists')).toBeVisible();
    
    // Verify form is still accessible
    await expect(page.locator('[placeholder*="repository name"]')).toBeVisible();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verify mobile layout
    await expect(page.locator('[data-testid="mobile-menu-toggle"]')).toBeVisible();
    
    // Test mobile navigation
    await page.click('[data-testid="mobile-menu-toggle"]');
    await expect(page.locator('text=Dashboard')).toBeVisible();
    await expect(page.locator('text=Repositories')).toBeVisible();
    
    // Test repository list on mobile
    await expect(page.locator('text=test-repo')).toBeVisible();
    
    // Test repository detail on mobile
    await page.click('text=test-repo');
    await expect(page.locator('h1')).toContainText('Repository Details');
  });

  test('should search and filter repositories', async ({ page }) => {
    // Add search functionality test
    await page.fill('[placeholder*="Search repositories"]', 'test');
    
    // Verify filtered results
    await expect(page.locator('text=test-repo')).toBeVisible();
    
    // Clear search
    await page.fill('[placeholder*="Search repositories"]', '');
    
    // Verify all repositories are shown again
    await expect(page.locator('text=test-repo')).toBeVisible();
  });
});
