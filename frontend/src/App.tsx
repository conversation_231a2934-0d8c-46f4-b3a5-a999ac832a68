import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import AppRoutes from './routes';
import { AuthProvider } from './contexts/AuthContext';
import AppLayout from './components/Layout/AppLayout';
import './App.css';

const App: React.FC = () => {
  return (
    <ConfigProvider>
      <AuthProvider>
        <Router>
          <AppLayout>
            <AppRoutes />
          </AppLayout>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
};

export default App; 