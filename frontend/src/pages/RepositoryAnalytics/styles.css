.analytics-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.analytics-breadcrumb {
  margin-bottom: 16px;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.error-container {
  max-width: 600px;
  margin: 100px auto;
  text-align: center;
}

.stats-row {
  margin-bottom: 24px;
}

.analytics-tabs {
  margin-bottom: 24px;
}

.chart-card {
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chart-container {
  margin-top: 24px;
  margin-bottom: 16px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .analytics-container {
    padding: 16px;
  }
  
  .analytics-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .chart-container {
    height: 250px !important;
  }
} 