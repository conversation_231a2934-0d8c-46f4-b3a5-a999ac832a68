import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  <PERSON>, 
  Row, 
  Col, 
  Typography, 
  Spin, 
  Alert, 
  DatePicker, 
  Button, 
  Tabs, 
  Statistic, 
  Divider,
  Select,
  Space,
  Breadcrumb,
  Empty
} from 'antd';
import { 
  <PERSON><PERSON><PERSON>Outlined, 
  Line<PERSON><PERSON>Outlined, 
  <PERSON><PERSON><PERSON>Outlined, 
  DownloadOutlined,
  HomeOutlined,
  AreaChartOutlined,
  FilterOutlined,
  FileExcelOutlined,
  FilePdfOutlined
} from '@ant-design/icons';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  AreaChart, 
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell
} from 'recharts';
import { repositoryService, commitService, milestoneService } from '../../services';
import './styles.css';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;

// Chart color palette
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

interface RepositoryData {
  id: number;
  name: string;
  full_name: string;
  description: string;
  created_at: string;
  updated_at: string;
  last_synced_at: string;
}

interface CommitData {
  date: string;
  count: number;
}

interface AuthorData {
  author: string;
  commits: number;
}

interface MilestoneData {
  status: string;
  count: number;
}

interface FileTypeData {
  type: string;
  count: number;
}

const RepositoryAnalytics: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [repository, setRepository] = useState<RepositoryData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null);
  const [commitsByDate, setCommitsByDate] = useState<CommitData[]>([]);
  const [commitsByAuthor, setCommitsByAuthor] = useState<AuthorData[]>([]);
  const [milestoneStats, setMilestoneStats] = useState<MilestoneData[]>([]);
  const [fileTypeStats, setFileTypeStats] = useState<FileTypeData[]>([]);
  const [exportLoading, setExportLoading] = useState<boolean>(false);

  useEffect(() => {
    fetchRepositoryData();
  }, [id]);

  useEffect(() => {
    if (repository) {
      fetchAnalyticsData();
    }
  }, [repository, dateRange]);

  const fetchRepositoryData = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await repositoryService.getRepositoryById(Number(id));
      setRepository(data);
    } catch (err) {
      console.error('Error fetching repository:', err);
      setError('Failed to load repository data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      // Fetch commits data
      const commitsResponse = await commitService.getCommitAnalytics(Number(id), {
        startDate: dateRange ? dateRange[0].toISOString() : undefined,
        endDate: dateRange ? dateRange[1].toISOString() : undefined
      });
      
      setCommitsByDate(commitsResponse.byDate);
      setCommitsByAuthor(commitsResponse.byAuthor);
      
      // Fetch milestone data
      const milestonesResponse = await milestoneService.getMilestoneAnalytics(Number(id));
      setMilestoneStats(milestonesResponse.byStatus);
      
      // Fetch file type data
      const fileTypesResponse = await repositoryService.getFileTypeAnalytics(Number(id));
      setFileTypeStats(fileTypesResponse);
      
    } catch (err) {
      console.error('Error fetching analytics data:', err);
      setError('Failed to load analytics data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange([dates[0].toDate(), dates[1].toDate()]);
    } else {
      setDateRange(null);
    }
  };

  const handleExport = async (format: 'csv' | 'pdf') => {
    setExportLoading(true);
    try {
      if (format === 'csv') {
        await repositoryService.exportRepositoryData(Number(id), 'csv');
      } else {
        await repositoryService.exportRepositoryData(Number(id), 'pdf');
      }
    } catch (err) {
      console.error(`Error exporting data as ${format}:`, err);
    } finally {
      setExportLoading(false);
    }
  };

  if (loading && !repository) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <Text>Loading repository analytics...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
        />
        <Button 
          type="primary" 
          onClick={fetchRepositoryData}
          style={{ marginTop: 16 }}
        >
          Retry
        </Button>
      </div>
    );
  }

  if (!repository) {
    return (
      <div className="error-container">
        <Alert
          message="Repository Not Found"
          description="The requested repository could not be found."
          type="warning"
          showIcon
        />
        <Button 
          type="primary" 
          href="/dashboard"
          style={{ marginTop: 16 }}
        >
          Back to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="analytics-container">
      <Breadcrumb className="analytics-breadcrumb">
        <Breadcrumb.Item href="/dashboard">
          <HomeOutlined /> Dashboard
        </Breadcrumb.Item>
        <Breadcrumb.Item href={`/repositories/${id}`}>
          {repository.name}
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <AreaChartOutlined /> Analytics
        </Breadcrumb.Item>
      </Breadcrumb>

      <div className="analytics-header">
        <div>
          <Title level={2}>
            <BarChartOutlined /> Repository Analytics
          </Title>
          <Text type="secondary">
            Data visualization and insights for {repository.name}
          </Text>
        </div>
        
        <Space>
          <RangePicker onChange={handleDateRangeChange} />
          
          <Select defaultValue="all" style={{ width: 120 }}>
            <Option value="all">All Data</Option>
            <Option value="commits">Commits Only</Option>
            <Option value="milestones">Milestones Only</Option>
          </Select>
          
          <Button 
            icon={<FileExcelOutlined />} 
            onClick={() => handleExport('csv')}
            loading={exportLoading}
          >
            Export CSV
          </Button>
          
          <Button 
            icon={<FilePdfOutlined />} 
            onClick={() => handleExport('pdf')}
            loading={exportLoading}
          >
            Export PDF
          </Button>
        </Space>
      </div>

      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic 
              title="Total Commits" 
              value={commitsByDate.reduce((sum, item) => sum + item.count, 0)} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic 
              title="Contributors" 
              value={commitsByAuthor.length} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic 
              title="Milestones" 
              value={milestoneStats.reduce((sum, item) => sum + item.count, 0)} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card>
            <Statistic 
              title="Special Files" 
              value={fileTypeStats.reduce((sum, item) => sum + item.count, 0)} 
            />
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="commits" className="analytics-tabs">
        <TabPane 
          tab={<span><LineChartOutlined /> Commit Activity</span>} 
          key="commits"
        >
          <Card className="chart-card">
            <Title level={4}>Commit Frequency</Title>
            <Paragraph>Number of commits over time</Paragraph>
            
            {commitsByDate.length > 0 ? (
              <div className="chart-container">
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={commitsByDate}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area 
                      type="monotone" 
                      dataKey="count" 
                      name="Commits" 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.3} 
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <Empty description="No commit data available" />
            )}
          </Card>
          
          <Card className="chart-card">
            <Title level={4}>Commits by Author</Title>
            <Paragraph>Distribution of commits across contributors</Paragraph>
            
            {commitsByAuthor.length > 0 ? (
              <div className="chart-container">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={commitsByAuthor}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="author" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar 
                      dataKey="commits" 
                      name="Commits" 
                      fill="#82ca9d" 
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <Empty description="No author data available" />
            )}
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><PieChartOutlined /> Milestones</span>} 
          key="milestones"
        >
          <Card className="chart-card">
            <Title level={4}>Milestone Status</Title>
            <Paragraph>Current status of repository milestones</Paragraph>
            
            {milestoneStats.length > 0 ? (
              <div className="chart-container">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={milestoneStats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="status"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {milestoneStats.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <Empty description="No milestone data available" />
            )}
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><BarChartOutlined /> Special Files</span>} 
          key="files"
        >
          <Card className="chart-card">
            <Title level={4}>Special Files by Type</Title>
            <Paragraph>Distribution of special configuration files</Paragraph>
            
            {fileTypeStats.length > 0 ? (
              <div className="chart-container">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={fileTypeStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar 
                      dataKey="count" 
                      name="Files" 
                      fill="#0088FE" 
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <Empty description="No special file data available" />
            )}
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default RepositoryAnalytics; 