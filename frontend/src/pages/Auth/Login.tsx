import React, { useEffect } from 'react';
import { Card, Button, Typography, Divider } from 'antd';
import { GithubOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './styles.css';

const { Title, Paragraph } = Typography;

const Login: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to dashboard if already authenticated
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleGitHubLogin = () => {
    // Redirect to backend GitHub OAuth endpoint
    window.location.href = `${process.env.REACT_APP_API_URL}/api/v1/auth/github/login`;
  };

  return (
    <div className="login-container">
      <Card className="login-card">
        <div className="login-header">
          <Title level={2}>RepoTimeline</Title>
          <Paragraph>
            Track and visualize the progress of your private GitHub repositories
          </Paragraph>
        </div>
        
        <Divider />
        
        <div className="login-content">
          <Paragraph>
            Sign in with your GitHub account to access your repositories and track their progress.
          </Paragraph>
          
          <Button 
            type="primary" 
            icon={<GithubOutlined />} 
            size="large" 
            onClick={handleGitHubLogin}
            className="github-button"
          >
            Sign in with GitHub
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default Login; 