import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, Typography, Spin, Alert } from 'antd';
import { useAuth } from '../../contexts/AuthContext';
import './styles.css';

const { Title, Paragraph } = Typography;

const Callback: React.FC = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Extract token from URL query parameters
        const params = new URLSearchParams(location.search);
        const token = params.get('token');
        
        if (!token) {
          setError('No authentication token received. Please try again.');
          return;
        }
        
        // Login with the token
        await login(token);
        
        // Redirect to dashboard
        navigate('/dashboard');
      } catch (err) {
        console.error('Authentication error:', err);
        setError('Failed to authenticate. Please try again.');
      }
    };
    
    processCallback();
  }, [location, login, navigate]);

  return (
    <div className="callback-container">
      <Card className="callback-card">
        {error ? (
          <Alert
            message="Authentication Error"
            description={error}
            type="error"
            showIcon
          />
        ) : (
          <>
            <Title level={3}>Authenticating...</Title>
            <Spin size="large" className="spinner" />
            <Paragraph>
              Please wait while we complete the authentication process.
            </Paragraph>
          </>
        )}
      </Card>
    </div>
  );
};

export default Callback; 