.user-profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.profile-header {
  margin-bottom: 24px;
}

.profile-card {
  display: flex;
  align-items: center;
  padding: 24px;
}

.profile-avatar {
  margin-right: 24px;
}

.profile-info {
  flex: 1;
}

.profile-tabs {
  margin-bottom: 24px;
}

.tab-card {
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Form styles */
.ant-form-item-label {
  font-weight: 500;
}

.ant-descriptions-title {
  font-size: 16px;
  font-weight: 500;
}

.ant-descriptions-bordered {
  margin-bottom: 24px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .user-profile-container {
    padding: 16px;
  }
  
  .profile-card {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-avatar {
    margin-right: 0;
    margin-bottom: 16px;
  }
  
  .ant-tabs-nav .ant-tabs-tab {
    margin: 0 8px 0 0;
    padding: 8px;
  }
  
  .ant-tabs-tab-btn {
    font-size: 14px;
  }
} 