import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Avatar, 
  Typography, 
  Tabs, 
  Form, 
  Input, 
  Button, 
  Switch, 
  Divider, 
  message, 
  Spin, 
  Alert,
  Space,
  Descriptions,
  Tag
} from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  GithubOutlined, 
  SaveOutlined, 
  BellOutlined, 
  SettingOutlined,
  KeyOutlined
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import './styles.css';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface UserProfile {
  id: number;
  username: string;
  email: string;
  name: string;
  avatar_url: string;
  github_username: string;
  github_id: string;
  created_at: string;
  updated_at: string;
}

interface UserSettings {
  email_notifications: boolean;
  sync_frequency: string;
  theme: string;
  timezone: string;
}

const UserProfile: React.FC = () => {
  const { user, updateUser } = useAuth();
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [profileForm] = Form.useForm();
  const [settingsForm] = Form.useForm();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [userSettings, setUserSettings] = useState<UserSettings | null>(null);

  useEffect(() => {
    fetchUserProfile();
    fetchUserSettings();
  }, []);

  const fetchUserProfile = async () => {
    setLoading(true);
    setError(null);
    try {
      // In a real app, this would be an API call
      // For now, we'll use the user data from auth context
      const profile = {
        id: 1,
        username: user?.username || 'user',
        email: user?.email || '<EMAIL>',
        name: user?.name || 'User Name',
        avatar_url: user?.avatar_url || '',
        github_username: user?.username || 'github_user',
        github_id: '12345678',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      setUserProfile(profile);
      profileForm.setFieldsValue({
        name: profile.name,
        email: profile.email,
        username: profile.username
      });
    } catch (err) {
      console.error('Error fetching user profile:', err);
      setError('Failed to load user profile. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserSettings = async () => {
    setLoading(true);
    setError(null);
    try {
      // In a real app, this would be an API call
      const settings = {
        email_notifications: true,
        sync_frequency: 'daily',
        theme: 'light',
        timezone: 'UTC'
      };
      
      setUserSettings(settings);
      settingsForm.setFieldsValue(settings);
    } catch (err) {
      console.error('Error fetching user settings:', err);
      setError('Failed to load user settings. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleProfileSave = async (values: any) => {
    setSaving(true);
    setError(null);
    try {
      // In a real app, this would be an API call
      console.log('Saving profile:', values);
      
      // Update the user profile
      setUserProfile(prev => prev ? { ...prev, ...values } : null);
      
      // Update the user in auth context
      if (updateUser) {
        updateUser({
          ...user,
          name: values.name,
          email: values.email,
          username: values.username
        });
      }
      
      message.success('Profile updated successfully');
    } catch (err) {
      console.error('Error updating profile:', err);
      setError('Failed to update profile. Please try again later.');
    } finally {
      setSaving(false);
    }
  };

  const handleSettingsSave = async (values: any) => {
    setSaving(true);
    setError(null);
    try {
      // In a real app, this would be an API call
      console.log('Saving settings:', values);
      
      // Update the user settings
      setUserSettings(prev => prev ? { ...prev, ...values } : null);
      
      message.success('Settings updated successfully');
    } catch (err) {
      console.error('Error updating settings:', err);
      setError('Failed to update settings. Please try again later.');
    } finally {
      setSaving(false);
    }
  };

  if (loading && !userProfile) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <Text>Loading user profile...</Text>
      </div>
    );
  }

  return (
    <div className="user-profile-container">
      <Title level={2}>User Profile</Title>

      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <div className="profile-header">
        <Card className="profile-card">
          <div className="profile-avatar">
            <Avatar 
              size={100} 
              src={userProfile?.avatar_url} 
              icon={<UserOutlined />} 
            />
          </div>
          <div className="profile-info">
            <Title level={3}>{userProfile?.name}</Title>
            <Space direction="vertical">
              <Text>
                <MailOutlined /> {userProfile?.email}
              </Text>
              <Text>
                <GithubOutlined /> {userProfile?.github_username}
              </Text>
              <Text type="secondary">
                Member since {new Date(userProfile?.created_at || '').toLocaleDateString()}
              </Text>
            </Space>
          </div>
        </Card>
      </div>

      <Tabs defaultActiveKey="profile" className="profile-tabs">
        <TabPane 
          tab={<span><UserOutlined /> Profile</span>} 
          key="profile"
        >
          <Card className="tab-card">
            <Form
              form={profileForm}
              layout="vertical"
              onFinish={handleProfileSave}
            >
              <Form.Item
                name="name"
                label="Full Name"
                rules={[{ required: true, message: 'Please enter your name' }]}
              >
                <Input prefix={<UserOutlined />} />
              </Form.Item>

              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Please enter your email' },
                  { type: 'email', message: 'Please enter a valid email' }
                ]}
              >
                <Input prefix={<MailOutlined />} />
              </Form.Item>

              <Form.Item
                name="username"
                label="Username"
                rules={[{ required: true, message: 'Please enter your username' }]}
              >
                <Input prefix={<GithubOutlined />} />
              </Form.Item>

              <Divider />

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<SaveOutlined />}
                  loading={saving}
                >
                  Save Profile
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane 
          tab={<span><SettingOutlined /> Settings</span>} 
          key="settings"
        >
          <Card className="tab-card">
            <Form
              form={settingsForm}
              layout="vertical"
              onFinish={handleSettingsSave}
            >
              <Form.Item
                name="email_notifications"
                label="Email Notifications"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="sync_frequency"
                label="Repository Sync Frequency"
              >
                <Input.Group compact>
                  <Input style={{ width: '100%' }} />
                </Input.Group>
              </Form.Item>

              <Form.Item
                name="theme"
                label="Theme"
              >
                <Input.Group compact>
                  <Input style={{ width: '100%' }} />
                </Input.Group>
              </Form.Item>

              <Form.Item
                name="timezone"
                label="Timezone"
              >
                <Input.Group compact>
                  <Input style={{ width: '100%' }} />
                </Input.Group>
              </Form.Item>

              <Divider />

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<SaveOutlined />}
                  loading={saving}
                >
                  Save Settings
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane 
          tab={<span><KeyOutlined /> API Keys</span>} 
          key="api-keys"
        >
          <Card className="tab-card">
            <Paragraph>
              Manage your API keys for accessing the RepoTimeline API programmatically.
            </Paragraph>
            
            <Descriptions title="Your API Keys" bordered>
              <Descriptions.Item label="Personal Access Token" span={3}>
                <Space>
                  <Tag color="blue">••••••••••••••••••••••</Tag>
                  <Button size="small">Show</Button>
                  <Button size="small">Regenerate</Button>
                </Space>
              </Descriptions.Item>
            </Descriptions>
            
            <Divider />
            
            <Paragraph>
              <Text strong>Note:</Text> Keep your API keys secure. Do not share them with others.
            </Paragraph>
          </Card>
        </TabPane>

        <TabPane 
          tab={<span><BellOutlined /> Notifications</span>} 
          key="notifications"
        >
          <Card className="tab-card">
            <Paragraph>
              Configure how and when you receive notifications about repository events.
            </Paragraph>
            
            <Form
              layout="vertical"
            >
              <Form.Item
                name="repo_updates"
                label="Repository Updates"
                valuePropName="checked"
              >
                <Switch defaultChecked />
              </Form.Item>
              
              <Form.Item
                name="milestone_reminders"
                label="Milestone Reminders"
                valuePropName="checked"
              >
                <Switch defaultChecked />
              </Form.Item>
              
              <Form.Item
                name="special_file_changes"
                label="Special File Changes"
                valuePropName="checked"
              >
                <Switch defaultChecked />
              </Form.Item>
              
              <Divider />
              
              <Form.Item>
                <Button 
                  type="primary" 
                  icon={<SaveOutlined />}
                >
                  Save Notification Settings
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default UserProfile; 