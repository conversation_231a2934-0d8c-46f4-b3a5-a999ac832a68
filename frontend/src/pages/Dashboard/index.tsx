import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Spin, Empty, Alert, Button, Typography, Space, Tooltip, message, Divider } from 'antd';
import { GithubOutlined, ClockCircleOutlined, CheckCircleOutlined, FileOutlined, PlusOutlined, StarOutlined, ForkOutlined, DeleteOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import api from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import './styles.css';
import './add-repository-styles.css';
import AddRepository from './AddRepository';

const { Title, Text } = Typography;

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  stars: number;
  forks: number;
  open_issues: number;
  last_synced_at: string;
}

interface Milestone {
  id: number;
  repository_id: number;
  title: string;
  description: string;
  due_date: string;
  is_completed: boolean;
}

interface SpecialFile {
  id: number;
  repository_id: number;
  file_type: string;
  file_path: string;
}

const Dashboard: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [upcomingMilestones, setUpcomingMilestones] = useState<Milestone[]>([]);
  const [specialFiles, setSpecialFiles] = useState<SpecialFile[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalRepositories: 0,
    totalSpecialFiles: 0,
    totalMilestones: 0,
    completedMilestones: 0
  });
  const [addRepoModalVisible, setAddRepoModalVisible] = useState<boolean>(false);

  useEffect(() => {
    if (isAuthenticated) {
      fetchDashboardData();
    }
  }, [isAuthenticated]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch repositories
      const reposResponse = await api.get('/api/v1/repositories');
      setRepositories(reposResponse.data);

      // Fetch upcoming milestones (not completed, due in the future)
      const now = new Date().toISOString();
      const twoWeeksLater = new Date();
      twoWeeksLater.setDate(new Date(now).getDate() + 14);
      const milestonesResponse = await api.get('/api/v1/milestones', {
        params: {
          is_completed: false,
          due_after: now,
          due_before: twoWeeksLater.toISOString()
        }
      });
      setUpcomingMilestones(milestonesResponse.data);

      // Fetch special files
      const specialFilesResponse = await api.get('/api/v1/special-files');
      setSpecialFiles(specialFilesResponse.data);

      // Calculate stats
      const specialFilesCount = await api.get('/api/v1/special-files/count');
      const milestonesStats = await api.get('/api/v1/milestones/stats');
      
      setStats({
        totalRepositories: reposResponse.data.length,
        totalSpecialFiles: specialFilesCount.data.total || 0,
        totalMilestones: milestonesStats.data.total || 0,
        completedMilestones: milestonesStats.data.completed || 0
      });
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const getSpecialFileCount = (repoId: number, fileType: string) => {
    return specialFiles.filter(
      file => file.repository_id === repoId && file.file_type === fileType
    ).length;
  };

  const repositoryColumns = [
    {
      title: 'Repository',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Repository) => (
        <Link to={`/repositories/${record.id}`}>
          <Space>
            <GithubOutlined />
            <span>{text}</span>
          </Space>
        </Link>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Special Files',
      key: 'special_files',
      render: (_: any, record: Repository) => (
        <>
          {getSpecialFileCount(record.id, 'dockerwrapper') > 0 && (
            <Tag color="blue">dockerwrapper</Tag>
          )}
          {getSpecialFileCount(record.id, 'claude') > 0 && (
            <Tag color="purple">claude</Tag>
          )}
          {getSpecialFileCount(record.id, 'cursor') > 0 && (
            <Tag color="green">cursor</Tag>
          )}
        </>
      ),
    },
    {
      title: 'Stats',
      key: 'stats',
      render: (_: any, record: Repository) => (
        <Space size="middle">
          <Tooltip title="Stars">
            <Tag icon={<StarOutlined />} color="blue">
              {record.stars}
            </Tag>
          </Tooltip>
          <Tooltip title="Forks">
            <Tag icon={<ForkOutlined />} color="green">
              {record.forks}
            </Tag>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const milestoneColumns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: 'Repository',
      key: 'repository',
      render: (_: any, record: Milestone) => {
        const repo = repositories.find(r => r.id === record.repository_id);
        return repo ? (
          <Link to={`/repositories/${repo.id}`}>{repo.name}</Link>
        ) : (
          'Unknown'
        );
      },
    },
    {
      title: 'Due Date',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : 'No due date',
    },
    {
      title: 'Status',
      key: 'status',
      render: (_: any, record: Milestone) => (
        <Tag color={record.is_completed ? 'green' : 'orange'}>
          {record.is_completed ? 'Completed' : 'In Progress'}
        </Tag>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <Text>Loading dashboard data...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error"
        description={error}
        type="error"
        showIcon
      />
    );
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <Title level={2}>Dashboard</Title>
        <Space>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => setAddRepoModalVisible(true)}
          >
            Add Repository
          </Button>
          <Link to="/trash">
            <Button icon={<DeleteOutlined />}>
              Trash
            </Button>
          </Link>
        </Space>
      </div>
      
      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={12} sm={12} md={6}>
          <Card className="dashboard-card">
            <Statistic 
              title="Total Repositories" 
              value={stats.totalRepositories} 
              prefix={<GithubOutlined />} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card className="dashboard-card">
            <Statistic 
              title="Special Files" 
              value={stats.totalSpecialFiles} 
              prefix={<FileOutlined />} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card className="dashboard-card">
            <Statistic 
              title="Total Milestones" 
              value={stats.totalMilestones} 
              prefix={<ClockCircleOutlined />} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={12} md={6}>
          <Card className="dashboard-card">
            <Statistic 
              title="Completed Milestones" 
              value={stats.completedMilestones} 
              suffix={`/ ${stats.totalMilestones}`}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      <Card 
        title={<Title level={4}>Your Repositories</Title>}
        className="repositories-card"
      >
        {repositories.length > 0 ? (
          <Table 
            dataSource={repositories} 
            columns={repositoryColumns} 
            rowKey="id"
            pagination={{ pageSize: 5 }}
          />
        ) : (
          <Empty 
            description="No repositories found" 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={() => setAddRepoModalVisible(true)}
            >
              Add Your First Repository
            </Button>
          </Empty>
        )}
      </Card>

      <Card 
        title={<Title level={4}>Upcoming Milestones</Title>}
        className="milestones-card"
      >
        {upcomingMilestones.length > 0 ? (
          <Table 
            dataSource={upcomingMilestones} 
            columns={milestoneColumns} 
            rowKey="id"
            pagination={{ pageSize: 5 }}
          />
        ) : (
          <Empty 
            description="No upcoming milestones" 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Card>

      <AddRepository 
        visible={addRepoModalVisible}
        onClose={() => setAddRepoModalVisible(false)}
        onSuccess={fetchDashboardData}
      />
    </div>
  );
};

export default Dashboard; 