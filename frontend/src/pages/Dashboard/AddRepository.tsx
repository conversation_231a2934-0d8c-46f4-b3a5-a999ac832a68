import React, { useState } from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Button, 
  message, 
  Select, 
  Divider, 
  Typography,
  Alert
} from 'antd';
import { 
  GithubOutlined, 
  PlusOutlined, 
  LoadingOutlined 
} from '@ant-design/icons';
import axios from 'axios';

const { Text, Title } = Typography;
const { Option } = Select;

interface AddRepositoryProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddRepository: React.FC<AddRepositoryProps> = ({ visible, onClose, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [selectedRepo, setSelectedRepo] = useState<any | null>(null);

  const handleSearch = async (value: string) => {
    if (!value || value.trim() === '') {
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);
    setSearchError(null);
    
    try {
      // Search for GitHub repositories
      const response = await axios.get(`/api/v1/github/search?q=${encodeURIComponent(value)}`);
      setSearchResults(response.data.items || []);
    } catch (error) {
      console.error('Error searching repositories:', error);
      setSearchError('Failed to search GitHub repositories. Please try again.');
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSelectRepo = (value: string) => {
    const repo = searchResults.find(r => r.full_name === value);
    setSelectedRepo(repo);
    
    if (repo) {
      form.setFieldsValue({
        name: repo.name,
        full_name: repo.full_name,
        description: repo.description || '',
        url: repo.url,
        html_url: repo.html_url,
        clone_url: repo.clone_url,
        default_branch: repo.default_branch,
      });
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const response = await axios.post('/api/v1/repositories', values);
      
      message.success('Repository added successfully!');
      form.resetFields();
      setSelectedRepo(null);
      setSearchResults([]);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error adding repository:', error);
      message.error('Failed to add repository. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedRepo(null);
    setSearchResults([]);
    onClose();
  };

  return (
    <Modal
      title="Add Repository"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          onClick={handleSubmit} 
          loading={loading}
          disabled={!selectedRepo}
        >
          Add Repository
        </Button>
      ]}
      width={600}
    >
      <div className="add-repository-container">
        <div className="search-section">
          <Title level={5}>Search GitHub Repository</Title>
          <Select
            showSearch
            placeholder="Search for a repository..."
            defaultActiveFirstOption={false}
            showArrow={false}
            filterOption={false}
            onSearch={handleSearch}
            onChange={handleSelectRepo}
            notFoundContent={searchLoading ? <LoadingOutlined /> : 'No repositories found'}
            style={{ width: '100%' }}
          >
            {searchResults.map(repo => (
              <Option key={repo.id} value={repo.full_name}>
                <div className="repo-option">
                  <GithubOutlined />
                  <span className="repo-name">{repo.full_name}</span>
                  <span className="repo-stars">⭐ {repo.stargazers_count}</span>
                </div>
              </Option>
            ))}
          </Select>
          
          {searchError && (
            <Alert 
              message={searchError} 
              type="error" 
              showIcon 
              style={{ marginTop: 8 }} 
            />
          )}
        </div>

        <Divider />

        <Form
          form={form}
          layout="vertical"
          name="add_repository_form"
        >
          <Form.Item
            name="name"
            label="Repository Name"
            rules={[{ required: true, message: 'Please enter repository name' }]}
          >
            <Input placeholder="Repository name" disabled={loading} />
          </Form.Item>
          
          <Form.Item
            name="full_name"
            label="Full Name"
            rules={[{ required: true, message: 'Please enter full repository name' }]}
          >
            <Input placeholder="owner/repository" disabled={loading} />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea 
              placeholder="Repository description" 
              rows={3} 
              disabled={loading} 
            />
          </Form.Item>
          
          <Form.Item
            name="html_url"
            label="GitHub URL"
            rules={[{ required: true, message: 'Please enter GitHub URL' }]}
          >
            <Input placeholder="https://github.com/owner/repository" disabled={loading} />
          </Form.Item>
          
          <Form.Item
            name="clone_url"
            label="Clone URL"
            rules={[{ required: true, message: 'Please enter clone URL' }]}
          >
            <Input placeholder="https://github.com/owner/repository.git" disabled={loading} />
          </Form.Item>
          
          <Form.Item
            name="default_branch"
            label="Default Branch"
            rules={[{ required: true, message: 'Please enter default branch' }]}
          >
            <Input placeholder="main" disabled={loading} />
          </Form.Item>
          
          <Form.Item
            name="url"
            label="API URL"
            hidden
          >
            <Input />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default AddRepository; 