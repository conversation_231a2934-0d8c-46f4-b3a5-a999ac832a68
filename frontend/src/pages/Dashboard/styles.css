.dashboard-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.stats-row {
  margin-bottom: 24px;
}

.dashboard-card {
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.repositories-card,
.milestones-card {
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.ant-tag {
  margin-right: 0;
}

@media (max-width: 768px) {
  .stats-row .ant-col {
    margin-bottom: 16px;
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .dashboard-container {
    padding: 16px;
  }
} 