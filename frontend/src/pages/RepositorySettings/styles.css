.repository-settings-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.settings-breadcrumb {
  margin-bottom: 16px;
}

.settings-card {
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.error-container {
  max-width: 600px;
  margin: 100px auto;
  text-align: center;
}

/* Form styles */
.ant-form-item-label {
  font-weight: 500;
}

.ant-form-item {
  margin-bottom: 20px;
}

.ant-divider {
  margin: 24px 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .repository-settings-container {
    padding: 16px;
  }
  
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  .ant-space {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .ant-space .ant-btn {
    width: 100%;
  }
} 