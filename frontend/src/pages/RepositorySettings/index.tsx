import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Switch, 
  Divider, 
  Typography, 
  Space, 
  Spin, 
  message, 
  Popconfirm,
  Alert,
  Breadcrumb,
  Select
} from 'antd';
import { 
  SaveOutlined, 
  DeleteOutlined, 
  UndoOutlined, 
  HomeOutlined, 
  SettingOutlined,
  GithubOutlined
} from '@ant-design/icons';
import { repositoryService } from '../../services';
import './styles.css';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  url: string;
  html_url: string;
  clone_url: string;
  default_branch: string;
  stars: number;
  forks: number;
  open_issues: number;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  last_synced_at: string;
}

const RepositorySettings: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [repository, setRepository] = useState<Repository | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [deleting, setDeleting] = useState<boolean>(false);
  const [restoring, setRestoring] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchRepository();
  }, [id]);

  const fetchRepository = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await repositoryService.getRepositoryById(Number(id), true);
      setRepository(data);
      form.setFieldsValue({
        name: data.name,
        full_name: data.full_name,
        description: data.description,
        html_url: data.html_url,
        clone_url: data.clone_url,
        default_branch: data.default_branch,
        is_active: data.is_active
      });
    } catch (err) {
      console.error('Error fetching repository:', err);
      setError('Failed to load repository data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values: any) => {
    setSaving(true);
    setError(null);
    try {
      await repositoryService.updateRepository(Number(id), values);
      message.success('Repository settings saved successfully');
      fetchRepository();
    } catch (err) {
      console.error('Error updating repository:', err);
      setError('Failed to save repository settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (permanent: boolean = false) => {
    setDeleting(true);
    setError(null);
    try {
      await repositoryService.deleteRepository(Number(id), permanent);
      message.success(
        permanent 
          ? 'Repository permanently deleted' 
          : 'Repository moved to trash'
      );
      if (permanent) {
        navigate('/dashboard');
      } else {
        fetchRepository();
      }
    } catch (err) {
      console.error('Error deleting repository:', err);
      setError('Failed to delete repository. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  const handleRestore = async () => {
    setRestoring(true);
    setError(null);
    try {
      await repositoryService.restoreRepository(Number(id));
      message.success('Repository restored successfully');
      fetchRepository();
    } catch (err) {
      console.error('Error restoring repository:', err);
      setError('Failed to restore repository. Please try again.');
    } finally {
      setRestoring(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <Text>Loading repository settings...</Text>
      </div>
    );
  }

  if (!repository) {
    return (
      <div className="error-container">
        <Alert
          message="Repository Not Found"
          description="The requested repository could not be found."
          type="error"
          showIcon
        />
        <Button 
          type="primary" 
          onClick={() => navigate('/dashboard')}
          style={{ marginTop: 16 }}
        >
          Back to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="repository-settings-container">
      <Breadcrumb className="settings-breadcrumb">
        <Breadcrumb.Item href="/dashboard">
          <HomeOutlined /> Dashboard
        </Breadcrumb.Item>
        <Breadcrumb.Item href={`/repositories/${id}`}>
          {repository.name}
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <SettingOutlined /> Settings
        </Breadcrumb.Item>
      </Breadcrumb>

      <Title level={2}>Repository Settings</Title>

      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {repository.is_deleted && (
        <Alert
          message="Repository in Trash"
          description="This repository has been moved to trash. You can restore it or delete it permanently."
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
          action={
            <Space>
              <Button 
                type="primary" 
                icon={<UndoOutlined />} 
                onClick={handleRestore}
                loading={restoring}
              >
                Restore
              </Button>
              <Popconfirm
                title="Permanently delete repository?"
                description="This action cannot be undone. All data will be permanently lost."
                onConfirm={() => handleDelete(true)}
                okText="Delete Permanently"
                cancelText="Cancel"
                okButtonProps={{ danger: true }}
              >
                <Button 
                  danger 
                  icon={<DeleteOutlined />} 
                  loading={deleting}
                >
                  Delete Permanently
                </Button>
              </Popconfirm>
            </Space>
          }
        />
      )}

      <Card className="settings-card">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          disabled={repository.is_deleted}
        >
          <Form.Item
            name="name"
            label="Repository Name"
            rules={[{ required: true, message: 'Please enter repository name' }]}
          >
            <Input prefix={<GithubOutlined />} />
          </Form.Item>

          <Form.Item
            name="full_name"
            label="Full Name"
            rules={[{ required: true, message: 'Please enter full repository name' }]}
          >
            <Input placeholder="owner/repository" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item
            name="html_url"
            label="GitHub URL"
            rules={[{ required: true, message: 'Please enter GitHub URL' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="clone_url"
            label="Clone URL"
            rules={[{ required: true, message: 'Please enter clone URL' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="default_branch"
            label="Default Branch"
            rules={[{ required: true, message: 'Please enter default branch' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="Active"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Divider />

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit" 
                icon={<SaveOutlined />}
                loading={saving}
                disabled={repository.is_deleted}
              >
                Save Changes
              </Button>
              
              {!repository.is_deleted && (
                <Popconfirm
                  title="Move repository to trash?"
                  description="You can restore it later if needed."
                  onConfirm={() => handleDelete(false)}
                  okText="Move to Trash"
                  cancelText="Cancel"
                >
                  <Button 
                    danger 
                    icon={<DeleteOutlined />} 
                    loading={deleting}
                  >
                    Move to Trash
                  </Button>
                </Popconfirm>
              )}
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card className="settings-card" title="Danger Zone">
        <Paragraph>
          Repository ID: <Text code>{repository.id}</Text>
        </Paragraph>
        <Paragraph>
          Created: <Text>{new Date(repository.created_at).toLocaleString()}</Text>
        </Paragraph>
        {repository.updated_at && (
          <Paragraph>
            Last Updated: <Text>{new Date(repository.updated_at).toLocaleString()}</Text>
          </Paragraph>
        )}
        {repository.deleted_at && (
          <Paragraph>
            Deleted: <Text>{new Date(repository.deleted_at).toLocaleString()}</Text>
          </Paragraph>
        )}
        {repository.last_synced_at && (
          <Paragraph>
            Last Synced: <Text>{new Date(repository.last_synced_at).toLocaleString()}</Text>
          </Paragraph>
        )}
      </Card>
    </div>
  );
};

export default RepositorySettings; 