.trash-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.trash-header {
  margin-bottom: 24px;
}

.trash-card {
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.error-message {
  margin-top: 16px;
  text-align: center;
}

/* Table styles */
.ant-table-wrapper {
  overflow-x: auto;
}

.ant-table-cell .ant-space {
  flex-wrap: wrap;
  gap: 8px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .trash-container {
    padding: 16px;
  }
  
  .ant-table-cell .ant-space {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .ant-table-cell .ant-space .ant-btn {
    width: 100%;
  }
} 