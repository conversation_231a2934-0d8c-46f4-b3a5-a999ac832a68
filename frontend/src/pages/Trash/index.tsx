import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Table, 
  Button, 
  Typography, 
  Space, 
  Popconfirm, 
  message, 
  Card, 
  Empty, 
  Spin,
  Tag,
  Tooltip
} from 'antd';
import { 
  DeleteOutlined, 
  UndoOutlined, 
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { repositoryService } from '../../services';
import './styles.css';

const { Title, Text } = Typography;

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  deleted_at: string;
}

const Trash: React.FC = () => {
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [actionInProgress, setActionInProgress] = useState<number | null>(null);

  useEffect(() => {
    fetchDeletedRepositories();
  }, []);

  const fetchDeletedRepositories = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await repositoryService.getDeletedRepositories();
      setRepositories(data);
    } catch (err) {
      console.error('Error fetching deleted repositories:', err);
      setError('Failed to load deleted repositories. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleRestore = async (id: number) => {
    setActionInProgress(id);
    try {
      await repositoryService.restoreRepository(id);
      message.success('Repository restored successfully');
      fetchDeletedRepositories();
    } catch (err) {
      console.error('Error restoring repository:', err);
      message.error('Failed to restore repository');
    } finally {
      setActionInProgress(null);
    }
  };

  const handlePermanentDelete = async (id: number) => {
    setActionInProgress(id);
    try {
      await repositoryService.deleteRepository(id, true);
      message.success('Repository permanently deleted');
      fetchDeletedRepositories();
    } catch (err) {
      console.error('Error permanently deleting repository:', err);
      message.error('Failed to permanently delete repository');
    } finally {
      setActionInProgress(null);
    }
  };

  const columns = [
    {
      title: 'Repository',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Repository) => (
        <Link to={`/repositories/${record.id}/settings`}>
          {record.full_name}
        </Link>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Deleted At',
      dataIndex: 'deleted_at',
      key: 'deleted_at',
      render: (date: string) => (
        <span>
          <ClockCircleOutlined /> {new Date(date).toLocaleString()}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (text: string, record: Repository) => (
        <Space size="small">
          <Tooltip title="Restore repository">
            <Button
              icon={<UndoOutlined />}
              onClick={() => handleRestore(record.id)}
              loading={actionInProgress === record.id}
            >
              Restore
            </Button>
          </Tooltip>
          <Tooltip title="Permanently delete">
            <Popconfirm
              title="Permanently delete repository?"
              description="This action cannot be undone. All data will be permanently lost."
              icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
              onConfirm={() => handlePermanentDelete(record.id)}
              okText="Delete Permanently"
              cancelText="Cancel"
              okButtonProps={{ danger: true }}
            >
              <Button 
                danger
                icon={<DeleteOutlined />}
                loading={actionInProgress === record.id}
              >
                Delete Permanently
              </Button>
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <Text>Loading trash...</Text>
      </div>
    );
  }

  return (
    <div className="trash-container">
      <div className="trash-header">
        <Title level={2}>
          <DeleteOutlined /> Trash
        </Title>
        <Text type="secondary">
          Repositories in trash will be automatically deleted after 30 days.
        </Text>
      </div>

      <Card className="trash-card">
        {repositories.length > 0 ? (
          <Table
            dataSource={repositories}
            columns={columns}
            rowKey="id"
            pagination={{ pageSize: 10 }}
          />
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="Trash is empty"
          />
        )}
      </Card>

      {error && (
        <div className="error-message">
          <Text type="danger">{error}</Text>
        </div>
      )}
    </div>
  );
};

export default Trash; 