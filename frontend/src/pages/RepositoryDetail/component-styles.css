/* Common styles for repository detail components */
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

/* Special Files Component Styles */
.special-files-container {
  padding: 16px;
}

.special-file-card {
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.special-file-content {
  max-height: 300px;
  overflow: auto;
  margin-bottom: 12px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.file-meta {
  display: flex;
  justify-content: flex-end;
  font-size: 12px;
}

/* Commits Component Styles */
.commits-container {
  padding: 16px;
}

.commit-item {
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
}

.commit-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.commit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.commit-meta {
  display: flex;
  gap: 16px;
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
}

.commit-body {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #f0f0f0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  margin-bottom: 24px;
}

/* Milestones Component Styles */
.milestones-container {
  padding: 16px;
}

.milestone-card {
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.milestone-actions {
  display: flex;
  gap: 4px;
}

.milestone-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.milestone-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 12px 0;
}

.milestone-progress {
  margin-top: auto;
  padding-top: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* Responsive styles */
@media (max-width: 768px) {
  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .commit-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .commit-meta {
    flex-direction: column;
    gap: 4px;
  }
  
  .milestone-meta {
    flex-direction: column;
    gap: 8px;
  }
} 