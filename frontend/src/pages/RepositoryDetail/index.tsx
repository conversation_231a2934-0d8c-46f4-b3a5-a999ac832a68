import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { 
  Card, 
  Tabs, 
  Button, 
  Descriptions, 
  Table, 
  Tag, 
  Timeline, 
  Spin, 
  Alert, 
  Divider,
  Typography,
  Space,
  Statistic,
  Row,
  Col,
  Breadcrumb,
  Tooltip
} from 'antd';
import { 
  GithubOutlined, 
  SyncOutlined, 
  FileOutlined, 
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StarOutlined,
  ForkOutlined,
  IssuesCloseOutlined,
  HistoryOutlined,
  FlagOutlined,
  HomeOutlined,
  SettingOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import api from '../../services/api';
import './styles.css';
import './component-styles.css';

const { TabPane } = Tabs;
const { Title, Paragraph, Text } = Typography;

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  url: string;
  html_url: string;
  clone_url: string;
  default_branch: string;
  stars: number;
  forks: number;
  open_issues: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_synced_at: string;
  special_files: SpecialFile[];
  commits: Commit[];
  milestones: Milestone[];
}

interface SpecialFile {
  id: number;
  repository_id: number;
  file_type: string;
  file_path: string;
  content: string;
  created_at: string;
  updated_at: string;
}

interface Commit {
  id: number;
  repository_id: number;
  sha: string;
  message: string;
  author: string;
  author_email: string;
  committed_date: string;
  created_at: string;
}

interface Milestone {
  id: number;
  repository_id: number;
  title: string;
  description: string;
  due_date: string;
  is_completed: boolean;
  completed_date: string;
  created_at: string;
  updated_at: string;
}

const RepositoryDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [repository, setRepository] = useState<Repository | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [syncingRepo, setSyncingRepo] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('special-files');

  useEffect(() => {
    fetchRepository();
  }, [id]);

  const fetchRepository = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.get(`/api/v1/repositories/${id}`);
      setRepository(response.data);
    } catch (err) {
      console.error('Error fetching repository:', err);
      setError('Failed to load repository data. Please try again later.');
      navigate('/dashboard');
    } finally {
      setLoading(false);
    }
  };

  const syncRepository = async () => {
    try {
      setSyncingRepo(true);
      await api.post(`/api/v1/repositories/${id}/sync`);
      message.success('Repository synced successfully');
      await fetchRepository();
    } catch (err) {
      console.error('Error syncing repository:', err);
      setError('Failed to sync repository. Please try again later.');
    } finally {
      setSyncingRepo(false);
    }
  };

  const syncSpecialFiles = async () => {
    try {
      await api.post(`/api/v1/special-files/sync/${id}`);
      message.success('Special files synced successfully');
      await fetchRepository();
    } catch (err) {
      console.error('Error syncing special files:', err);
      setError('Failed to sync special files. Please try again later.');
    }
  };

  const syncCommits = async () => {
    try {
      await api.post(`/api/v1/commits/sync/${id}`);
      message.success('Commits synced successfully');
      await fetchRepository();
    } catch (err) {
      console.error('Error syncing commits:', err);
      setError('Failed to sync commits. Please try again later.');
    }
  };

  const renderSpecialFileContent = (content: string, fileType: string) => {
    if (!content) return <Text>No content available</Text>;
    
    // For now, just render as pre-formatted text
    // In a real app, you might want to add syntax highlighting or other formatting
    return (
      <pre className="special-file-content">
        {content}
      </pre>
    );
  };

  const commitColumns = [
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    {
      title: 'Author',
      dataIndex: 'author',
      key: 'author',
    },
    {
      title: 'Date',
      dataIndex: 'committed_date',
      key: 'committed_date',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: 'SHA',
      dataIndex: 'sha',
      key: 'sha',
      render: (sha: string) => sha.substring(0, 7),
    },
  ];

  const milestoneColumns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Due Date',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : 'No due date',
    },
    {
      title: 'Status',
      key: 'status',
      render: (_: any, record: Milestone) => (
        <Tag color={record.is_completed ? 'green' : 'orange'}>
          {record.is_completed ? 'Completed' : 'In Progress'}
        </Tag>
      ),
    },
    {
      title: 'Completed Date',
      dataIndex: 'completed_date',
      key: 'completed_date',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-',
    },
  ];

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <Text>Loading repository details...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error"
        description={error}
        type="error"
        showIcon
      />
    );
  }

  if (!repository) {
    return (
      <div className="loading-container">
        <Text>Repository not found</Text>
        <Button type="primary" onClick={() => navigate('/dashboard')}>
          Back to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="repository-detail-container">
      <Breadcrumb className="repository-breadcrumb">
        <Breadcrumb.Item href="/dashboard">
          <HomeOutlined /> Dashboard
        </Breadcrumb.Item>
        <Breadcrumb.Item>{repository.name}</Breadcrumb.Item>
      </Breadcrumb>

      <div className="repository-header">
        <div className="repository-title">
          <Title level={2}>{repository.name}</Title>
          <Text type="secondary">{repository.description}</Text>
          <div className="repository-links">
            <Space>
              <a href={repository.html_url} target="_blank" rel="noopener noreferrer">
                View on GitHub
              </a>
              <Divider type="vertical" />
              <Link to={`/repositories/${repository.id}/analytics`}>
                <BarChartOutlined /> Analytics
              </Link>
              <Divider type="vertical" />
              <Link to={`/repositories/${repository.id}/settings`}>
                <SettingOutlined /> Settings
              </Link>
            </Space>
          </div>
        </div>
        <Button 
          type="primary" 
          icon={<SyncOutlined />} 
          loading={syncingRepo}
          onClick={syncRepository}
        >
          Sync Repository
        </Button>
      </div>

      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={12} sm={8} md={6}>
          <Card>
            <Statistic 
              title="Stars" 
              value={repository.stars} 
              prefix={<StarOutlined />} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6}>
          <Card>
            <Statistic 
              title="Forks" 
              value={repository.forks} 
              prefix={<ForkOutlined />} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6}>
          <Card>
            <Statistic 
              title="Open Issues" 
              value={repository.open_issues} 
              prefix={<IssuesCloseOutlined />} 
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6}>
          <Card>
            <Statistic 
              title="Last Synced" 
              value={new Date(repository.last_synced_at).toLocaleDateString()} 
              prefix={<ClockCircleOutlined />} 
            />
          </Card>
        </Col>
      </Row>

      <Divider />

      <div className="repository-tabs">
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
        >
          <TabPane 
            tab={<span><FileOutlined /> Special Files</span>} 
            key="special-files"
          >
            <SpecialFiles 
              repositoryId={repository.id} 
              specialFiles={repository.special_files || []} 
              onSync={syncSpecialFiles} 
            />
          </TabPane>
          <TabPane 
            tab={<span><HistoryOutlined /> Commits</span>} 
            key="commits"
          >
            <Commits 
              repositoryId={repository.id} 
              commits={repository.commits || []} 
              onSync={syncCommits} 
            />
          </TabPane>
          <TabPane 
            tab={<span><FlagOutlined /> Milestones</span>} 
            key="milestones"
          >
            <Milestones 
              repositoryId={repository.id} 
              milestones={repository.milestones || []} 
              onSync={fetchRepository} 
            />
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default RepositoryDetail; 