import React, { useState } from 'react';
import { List, Avatar, Typography, Button, Tag, Tooltip, Empty, Pagination } from 'antd';
import { SyncOutlined, UserOutlined, CalendarOutlined, CodeOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Title, Text, Paragraph } = Typography;

interface Commit {
  id: number;
  repository_id: number;
  sha: string;
  message: string;
  author: string;
  author_email: string;
  committed_date: string;
  created_at: string;
}

interface CommitsProps {
  repositoryId: number;
  commits: Commit[];
  onSync: () => void;
}

const Commits: React.FC<CommitsProps> = ({ repositoryId, commits, onSync }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const pageSize = 10;

  const handleSyncCommits = async () => {
    setLoading(true);
    try {
      await onSync();
    } catch (error) {
      console.error('Error syncing commits:', error);
    } finally {
      setLoading(false);
    }
  };

  const paginatedCommits = commits.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getAvatarColor = (author: string): string => {
    // Generate a consistent color based on the author's name
    const hash = author.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const hue = hash % 360;
    return `hsl(${hue}, 70%, 60%)`;
  };

  const formatCommitMessage = (message: string): { title: string; body: string } => {
    const parts = message.split('\n\n');
    return {
      title: parts[0],
      body: parts.slice(1).join('\n\n')
    };
  };

  return (
    <div className="commits-container">
      <div className="tab-header">
        <Title level={4}>Repository Commits</Title>
        <Button 
          type="primary" 
          icon={<SyncOutlined />} 
          onClick={handleSyncCommits}
          loading={loading}
        >
          Sync Commits
        </Button>
      </div>

      {commits.length === 0 ? (
        <Empty 
          description="No commits found in this repository" 
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
        />
      ) : (
        <>
          <List
            itemLayout="vertical"
            dataSource={paginatedCommits}
            renderItem={(commit) => {
              const { title, body } = formatCommitMessage(commit.message);
              return (
                <List.Item
                  key={commit.id}
                  className="commit-item"
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        style={{ backgroundColor: getAvatarColor(commit.author) }}
                        icon={<UserOutlined />}
                      />
                    }
                    title={
                      <div className="commit-header">
                        <Text strong>{title}</Text>
                        <Tooltip title={commit.sha}>
                          <Tag color="blue">{commit.sha.substring(0, 7)}</Tag>
                        </Tooltip>
                      </div>
                    }
                    description={
                      <div className="commit-meta">
                        <span>
                          <UserOutlined /> {commit.author}
                          {commit.author_email && ` <${commit.author_email}>`}
                        </span>
                        <span>
                          <CalendarOutlined /> {new Date(commit.committed_date).toLocaleString()}
                        </span>
                      </div>
                    }
                  />
                  {body && (
                    <div className="commit-body">
                      <Paragraph ellipsis={{ rows: 3, expandable: true, symbol: 'more' }}>
                        {body}
                      </Paragraph>
                    </div>
                  )}
                </List.Item>
              );
            }}
          />
          <div className="pagination-container">
            <Pagination
              current={currentPage}
              total={commits.length}
              pageSize={pageSize}
              onChange={handlePageChange}
              showSizeChanger={false}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default Commits; 