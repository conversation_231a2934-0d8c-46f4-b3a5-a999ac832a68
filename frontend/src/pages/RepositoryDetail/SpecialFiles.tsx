import React, { useState, useEffect } from 'react';
import { Card, List, Tag, Button, Modal, Form, Input, message, Spin, Empty, Typography } from 'antd';
import { FileOutlined, EditOutlined, DeleteOutlined, SyncOutlined } from '@ant-design/icons';
import axios from 'axios';
import Syntax<PERSON>ighlighter from 'react-syntax-highlighter';
import { docco } from 'react-syntax-highlighter/dist/esm/styles/hljs';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface SpecialFile {
  id: number;
  repository_id: number;
  file_type: string;
  file_path: string;
  content: string;
  created_at: string;
  updated_at: string;
}

interface SpecialFilesProps {
  repositoryId: number;
  specialFiles: SpecialFile[];
  onSync: () => void;
}

const SpecialFiles: React.FC<SpecialFilesProps> = ({ repositoryId, specialFiles, onSync }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [currentFile, setCurrentFile] = useState<SpecialFile | null>(null);
  const [form] = Form.useForm();

  const getFileTypeColor = (fileType: string): string => {
    switch (fileType) {
      case 'dockerwrapper':
        return 'blue';
      case 'claude':
        return 'purple';
      case 'cursor':
        return 'green';
      default:
        return 'default';
    }
  };

  const handleEditFile = (file: SpecialFile) => {
    setCurrentFile(file);
    form.setFieldsValue({
      file_path: file.file_path,
      content: file.content,
    });
    setEditModalVisible(true);
  };

  const handleUpdateFile = async (values: any) => {
    if (!currentFile) return;
    
    setLoading(true);
    try {
      await axios.put(`/api/v1/special-files/${currentFile.id}`, {
        file_path: values.file_path,
        content: values.content,
      });
      message.success('Special file updated successfully');
      setEditModalVisible(false);
      onSync();
    } catch (error) {
      console.error('Error updating special file:', error);
      message.error('Failed to update special file');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFile = async (fileId: number) => {
    Modal.confirm({
      title: 'Are you sure you want to delete this file?',
      content: 'This action can be undone later.',
      onOk: async () => {
        setLoading(true);
        try {
          await axios.delete(`/api/v1/special-files/${fileId}`);
          message.success('Special file deleted successfully');
          onSync();
        } catch (error) {
          console.error('Error deleting special file:', error);
          message.error('Failed to delete special file');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  return (
    <div className="special-files-container">
      <div className="tab-header">
        <Title level={4}>Special Configuration Files</Title>
        <Button 
          type="primary" 
          icon={<SyncOutlined />} 
          onClick={onSync}
          loading={loading}
        >
          Sync Files
        </Button>
      </div>

      {specialFiles.length === 0 ? (
        <Empty 
          description="No special files found in this repository" 
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
        />
      ) : (
        <List
          grid={{ gutter: 16, xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 3 }}
          dataSource={specialFiles}
          renderItem={(file) => (
            <List.Item>
              <Card 
                className="special-file-card"
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <FileOutlined style={{ marginRight: 8 }} />
                    <Text ellipsis style={{ maxWidth: '70%' }}>{file.file_path}</Text>
                    <Tag color={getFileTypeColor(file.file_type)} style={{ marginLeft: 8 }}>
                      {file.file_type}
                    </Tag>
                  </div>
                }
                extra={
                  <div>
                    <Button 
                      icon={<EditOutlined />} 
                      type="text" 
                      onClick={() => handleEditFile(file)}
                    />
                    <Button 
                      icon={<DeleteOutlined />} 
                      type="text" 
                      danger 
                      onClick={() => handleDeleteFile(file.id)}
                    />
                  </div>
                }
              >
                <div className="special-file-content">
                  <SyntaxHighlighter language="yaml" style={docco} showLineNumbers>
                    {file.content || '# No content available'}
                  </SyntaxHighlighter>
                </div>
                <div className="file-meta">
                  <Text type="secondary">
                    Last updated: {new Date(file.updated_at || file.created_at).toLocaleString()}
                  </Text>
                </div>
              </Card>
            </List.Item>
          )}
        />
      )}

      <Modal
        title="Edit Special File"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdateFile}
        >
          <Form.Item
            name="file_path"
            label="File Path"
            rules={[{ required: true, message: 'Please enter the file path' }]}
          >
            <Input placeholder="Path to the file in the repository" />
          </Form.Item>
          <Form.Item
            name="content"
            label="Content"
          >
            <TextArea rows={15} placeholder="File content" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              Save Changes
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SpecialFiles; 