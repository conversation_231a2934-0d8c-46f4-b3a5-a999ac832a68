import React, { useState } from 'react';
import { 
  List, 
  Card, 
  Typography, 
  Button, 
  Tag, 
  Progress, 
  Modal, 
  Form, 
  Input, 
  DatePicker, 
  message, 
  Popconfirm, 
  Empty 
} from 'antd';
import { 
  SyncOutlined, 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined 
} from '@ant-design/icons';
import axios from 'axios';
import moment from 'moment';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface Milestone {
  id: number;
  repository_id: number;
  title: string;
  description: string;
  due_date: string;
  is_completed: boolean;
  completed_date: string;
  created_at: string;
  updated_at: string;
}

interface MilestonesProps {
  repositoryId: number;
  milestones: Milestone[];
  onSync: () => void;
}

const Milestones: React.FC<MilestonesProps> = ({ repositoryId, milestones, onSync }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [currentMilestone, setCurrentMilestone] = useState<Milestone | null>(null);
  const [form] = Form.useForm();

  const handleAddMilestone = () => {
    setEditMode(false);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditMilestone = (milestone: Milestone) => {
    setEditMode(true);
    setCurrentMilestone(milestone);
    form.setFieldsValue({
      title: milestone.title,
      description: milestone.description,
      due_date: milestone.due_date ? moment(milestone.due_date) : undefined,
    });
    setModalVisible(true);
  };

  const handleSaveMilestone = async (values: any) => {
    setLoading(true);
    try {
      const payload = {
        title: values.title,
        description: values.description,
        due_date: values.due_date ? values.due_date.toISOString() : null,
      };

      if (editMode && currentMilestone) {
        await axios.put(`/api/v1/milestones/${currentMilestone.id}`, payload);
        message.success('Milestone updated successfully');
      } else {
        await axios.post('/api/v1/milestones', {
          ...payload,
          repository_id: repositoryId,
        });
        message.success('Milestone created successfully');
      }
      
      setModalVisible(false);
      onSync();
    } catch (error) {
      console.error('Error saving milestone:', error);
      message.error('Failed to save milestone');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMilestone = async (milestoneId: number) => {
    setLoading(true);
    try {
      await axios.delete(`/api/v1/milestones/${milestoneId}`);
      message.success('Milestone deleted successfully');
      onSync();
    } catch (error) {
      console.error('Error deleting milestone:', error);
      message.error('Failed to delete milestone');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleMilestoneStatus = async (milestone: Milestone) => {
    setLoading(true);
    try {
      if (milestone.is_completed) {
        await axios.post(`/api/v1/milestones/${milestone.id}/reopen`);
        message.success('Milestone reopened');
      } else {
        await axios.post(`/api/v1/milestones/${milestone.id}/complete`);
        message.success('Milestone completed');
      }
      onSync();
    } catch (error) {
      console.error('Error updating milestone status:', error);
      message.error('Failed to update milestone status');
    } finally {
      setLoading(false);
    }
  };

  const getMilestoneStatus = (milestone: Milestone) => {
    if (milestone.is_completed) {
      return {
        color: 'success',
        text: 'Completed',
        icon: <CheckCircleOutlined />,
      };
    }

    if (milestone.due_date) {
      const dueDate = new Date(milestone.due_date);
      const now = new Date();
      
      if (dueDate < now) {
        return {
          color: 'error',
          text: 'Overdue',
          icon: <ClockCircleOutlined />,
        };
      } else {
        // Calculate days remaining
        const daysRemaining = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        return {
          color: daysRemaining <= 7 ? 'warning' : 'processing',
          text: `${daysRemaining} days remaining`,
          icon: <ClockCircleOutlined />,
        };
      }
    }

    return {
      color: 'default',
      text: 'No due date',
      icon: null,
    };
  };

  return (
    <div className="milestones-container">
      <div className="tab-header">
        <Title level={4}>Repository Milestones</Title>
        <div>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAddMilestone}
            style={{ marginRight: 8 }}
          >
            Add Milestone
          </Button>
          <Button 
            icon={<SyncOutlined />} 
            onClick={onSync}
            loading={loading}
          >
            Refresh
          </Button>
        </div>
      </div>

      {milestones.length === 0 ? (
        <Empty 
          description="No milestones found for this repository" 
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
        />
      ) : (
        <List
          grid={{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 3 }}
          dataSource={milestones}
          renderItem={(milestone) => {
            const status = getMilestoneStatus(milestone);
            return (
              <List.Item>
                <Card 
                  className="milestone-card"
                  title={milestone.title}
                  extra={
                    <div className="milestone-actions">
                      <Button 
                        icon={<EditOutlined />} 
                        type="text" 
                        onClick={() => handleEditMilestone(milestone)}
                      />
                      <Popconfirm
                        title="Are you sure you want to delete this milestone?"
                        onConfirm={() => handleDeleteMilestone(milestone.id)}
                        okText="Yes"
                        cancelText="No"
                      >
                        <Button 
                          icon={<DeleteOutlined />} 
                          type="text" 
                          danger 
                        />
                      </Popconfirm>
                    </div>
                  }
                >
                  <div className="milestone-content">
                    {milestone.description && (
                      <Paragraph ellipsis={{ rows: 3, expandable: true, symbol: 'more' }}>
                        {milestone.description}
                      </Paragraph>
                    )}
                    
                    <div className="milestone-meta">
                      <Tag color={status.color} icon={status.icon}>
                        {status.text}
                      </Tag>
                      
                      {milestone.due_date && (
                        <Text type="secondary">
                          Due: {new Date(milestone.due_date).toLocaleDateString()}
                        </Text>
                      )}
                      
                      {milestone.is_completed && milestone.completed_date && (
                        <Text type="secondary">
                          Completed: {new Date(milestone.completed_date).toLocaleDateString()}
                        </Text>
                      )}
                    </div>
                    
                    <div className="milestone-progress">
                      <Progress 
                        percent={milestone.is_completed ? 100 : 0} 
                        status={milestone.is_completed ? "success" : "active"} 
                        showInfo={false}
                      />
                      <Button 
                        type="link"
                        onClick={() => handleToggleMilestoneStatus(milestone)}
                      >
                        {milestone.is_completed ? 'Reopen' : 'Mark as Complete'}
                      </Button>
                    </div>
                  </div>
                </Card>
              </List.Item>
            );
          }}
        />
      )}

      <Modal
        title={editMode ? "Edit Milestone" : "Add Milestone"}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveMilestone}
        >
          <Form.Item
            name="title"
            label="Title"
            rules={[{ required: true, message: 'Please enter a title' }]}
          >
            <Input placeholder="Milestone title" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea rows={4} placeholder="Milestone description" />
          </Form.Item>
          
          <Form.Item
            name="due_date"
            label="Due Date"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              {editMode ? 'Update' : 'Create'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Milestones; 