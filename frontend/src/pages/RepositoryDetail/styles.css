.repository-detail-container {
  padding: 20px;
}

.repository-header {
  margin-bottom: 24px;
}

.repository-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.repository-tabs {
  background-color: white;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.special-file-card {
  margin-bottom: 16px;
}

.special-file-content {
  max-height: 400px;
  overflow: auto;
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

@media (max-width: 768px) {
  .repository-title {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .repository-title h2 {
    margin-bottom: 16px;
  }
  
  .tab-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .tab-header h4 {
    margin-bottom: 16px;
  }
} 