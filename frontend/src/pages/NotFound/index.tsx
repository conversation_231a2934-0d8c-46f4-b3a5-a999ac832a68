import React from 'react';
import { Result, Button } from 'antd';
import { Link } from 'react-router-dom';
import './styles.css';

const NotFound: React.FC = () => {
  return (
    <div className="not-found-container">
      <Result
        status="404"
        title="404"
        subTitle="Sorry, the page you visited does not exist."
        extra={
          <Link to="/dashboard">
            <Button type="primary">Back to Dashboard</Button>
          </Link>
        }
      />
    </div>
  );
};

export default NotFound; 