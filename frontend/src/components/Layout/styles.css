.app-layout {
  min-height: 100vh;
}

.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.logo {
  font-size: 18px;
  font-weight: 600;
}

.logo a {
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.user-menu {
  display: flex;
  align-items: center;
}

.user-dropdown {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.app-content {
  padding: 0;
  background-color: #f0f2f5;
}

.app-footer {
  text-align: center;
  background-color: #fff;
  padding: 16px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
  }
  
  .header-menu {
    display: none;
  }
  
  .logo {
    font-size: 16px;
  }
} 