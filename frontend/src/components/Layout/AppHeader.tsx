import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Layout, 
  Menu, 
  Dropdown, 
  Avatar, 
  Button, 
  Space, 
  Divider,
  Typography
} from 'antd';
import { 
  UserOutlined, 
  SettingOutlined, 
  LogoutOutlined, 
  GithubOutlined,
  DashboardOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import './styles.css';

const { Header } = Layout;
const { Text } = Typography;

const AppHeader: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        <Link to="/profile">Profile</Link>
      </Menu.Item>
      <Menu.Item key="settings" icon={<SettingOutlined />}>
        <Link to="/profile?tab=settings">Settings</Link>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        Logout
      </Menu.Item>
    </Menu>
  );

  return (
    <Header className="app-header">
      <div className="logo">
        <Link to="/dashboard">
          <GithubOutlined /> RepoTimeline
        </Link>
      </div>
      
      <div className="header-menu">
        <Space size="large">
          <Link to="/dashboard">
            <Button type="text" icon={<DashboardOutlined />}>
              Dashboard
            </Button>
          </Link>
          
          <Link to="/trash">
            <Button type="text" icon={<DeleteOutlined />}>
              Trash
            </Button>
          </Link>
        </Space>
      </div>
      
      <div className="user-menu">
        {user ? (
          <Dropdown overlay={userMenu} trigger={['click']} placement="bottomRight">
            <Space className="user-dropdown">
              <Avatar 
                src={user.avatar_url} 
                icon={<UserOutlined />} 
                size="small" 
              />
              <Text>{user.name || user.username}</Text>
            </Space>
          </Dropdown>
        ) : (
          <Link to="/login">
            <Button type="primary">Login</Button>
          </Link>
        )}
      </div>
    </Header>
  );
};

export default AppHeader; 