import React from 'react';
import { Layout } from 'antd';

const { Header, Content, Footer } = Layout;

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  return (
    <Layout className="app-container">
      <Header style={{ background: '#fff', padding: '0 24px' }}>
        <h1>DashDevil</h1>
      </Header>
      <Content style={{ padding: '24px', minHeight: 280 }}>
        {children}
      </Content>
      <Footer style={{ textAlign: 'center' }}>
        DashDevil ©{new Date().getFullYear()}
      </Footer>
    </Layout>
  );
};

export default AppLayout; 