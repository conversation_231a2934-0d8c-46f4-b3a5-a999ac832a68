import React, { ReactNode, useState } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  GithubOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './AppLayout.css';

const { Header, Sider, Content } = Layout;

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, isAuthenticated, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // If not authenticated and not on login page, render minimal layout
  if (!isAuthenticated && !location.pathname.startsWith('/auth')) {
    return (
      <Layout className="app-layout">
        <Content className="app-content">{children}</Content>
      </Layout>
    );
  }

  const userMenuItems = [
    {
      key: 'profile',
      label: 'Profile',
      icon: <UserOutlined />,
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      label: 'Settings',
      icon: <SettingOutlined />,
      onClick: () => navigate('/settings'),
    },
    {
      key: 'logout',
      label: 'Logout',
      icon: <LogoutOutlined />,
      onClick: () => {
        logout();
        navigate('/auth/login');
      },
    },
  ];

  return (
    <Layout className="app-layout">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={250}
        className="app-sider"
      >
        <div className="logo">
          {collapsed ? 'RT' : 'RepoTimeline'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={[
            {
              key: '/dashboard',
              icon: <DashboardOutlined />,
              label: <Link to="/dashboard">Dashboard</Link>,
            },
            {
              key: '/repositories',
              icon: <GithubOutlined />,
              label: <Link to="/repositories">Repositories</Link>,
            },
            {
              key: '/analytics',
              icon: <BarChartOutlined />,
              label: <Link to="/analytics">Analytics</Link>,
            },
            {
              key: '/settings',
              icon: <SettingOutlined />,
              label: <Link to="/settings">Settings</Link>,
            },
          ]}
        />
      </Sider>
      <Layout>
        <Header className="app-header">
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            className="trigger-button"
          />
          <div className="header-right">
            {user && (
              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <Space className="user-dropdown">
                  <Avatar icon={<UserOutlined />} />
                  {!collapsed && <span className="username">{user.username}</span>}
                </Space>
              </Dropdown>
            )}
          </div>
        </Header>
        <Content className="app-content">{children}</Content>
      </Layout>
    </Layout>
  );
};

export default AppLayout; 