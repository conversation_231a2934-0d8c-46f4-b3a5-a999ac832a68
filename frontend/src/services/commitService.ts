import axios from 'axios';

const API_BASE_URL = '/api/v1';

interface AnalyticsOptions {
  startDate?: string;
  endDate?: string;
  author?: string;
}

/**
 * Service for interacting with commit API endpoints
 */
const commitService = {
  /**
   * Get all commits
   * @param repositoryId Optional repository ID to filter by
   * @param author Optional author to filter by
   * @param since Optional date to filter commits after
   * @param until Optional date to filter commits before
   * @param includeDeleted Whether to include soft-deleted commits
   * @returns Promise with commits
   */
  getAllCommits: async (
    repositoryId?: number,
    author?: string,
    since?: string,
    until?: string,
    includeDeleted: boolean = false
  ) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/commits`, {
        params: {
          repository_id: repositoryId,
          author,
          since,
          until,
          include_deleted: includeDeleted
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching commits:', error);
      throw error;
    }
  },

  /**
   * Get commit by ID
   * @param id Commit ID
   * @param includeDeleted Whether to include soft-deleted commits
   * @returns Promise with commit details
   */
  getCommitById: async (id: number, includeDeleted: boolean = false) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/commits/${id}`, {
        params: { include_deleted: includeDeleted }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching commit with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new commit
   * @param commitData Commit data
   * @returns Promise with created commit
   */
  createCommit: async (commitData: any) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/commits`, commitData);
      return response.data;
    } catch (error) {
      console.error('Error creating commit:', error);
      throw error;
    }
  },

  /**
   * Update a commit
   * @param id Commit ID
   * @param commitData Commit data to update
   * @returns Promise with updated commit
   */
  updateCommit: async (id: number, commitData: any) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/commits/${id}`, commitData);
      return response.data;
    } catch (error) {
      console.error(`Error updating commit with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a commit
   * @param id Commit ID
   * @param permanent Whether to permanently delete
   * @returns Promise with deletion result
   */
  deleteCommit: async (id: number, permanent: boolean = false) => {
    try {
      const response = await axios.delete(`${API_BASE_URL}/commits/${id}`, {
        params: { permanent }
      });
      return response.data;
    } catch (error) {
      console.error(`Error deleting commit with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Restore a soft-deleted commit
   * @param id Commit ID
   * @returns Promise with restored commit
   */
  restoreCommit: async (id: number) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/commits/${id}/restore`);
      return response.data;
    } catch (error) {
      console.error(`Error restoring commit with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Sync commits for a repository
   * @param repositoryId Repository ID
   * @param limit Maximum number of commits to sync
   * @returns Promise with synced commits
   */
  syncCommits: async (repositoryId: number, limit: number = 100) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/commits/sync/${repositoryId}`, null, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error(`Error syncing commits for repository ${repositoryId}:`, error);
      throw error;
    }
  },

  /**
   * Get commit statistics
   * @param repositoryId Optional repository ID to filter by
   * @returns Promise with commit statistics
   */
  getCommitStats: async (repositoryId?: number) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/commits/stats`, {
        params: { repository_id: repositoryId }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching commit statistics:', error);
      throw error;
    }
  },

  /**
   * Get commit analytics data
   * @param repositoryId Repository ID
   * @param options Analytics options (date range, author)
   * @returns Promise with commit analytics data
   */
  getCommitAnalytics: async (repositoryId: number, options?: AnalyticsOptions) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/commits/analytics`, {
        params: {
          repository_id: repositoryId,
          start_date: options?.startDate,
          end_date: options?.endDate,
          author: options?.author
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching commit analytics for repository ${repositoryId}:`, error);
      throw error;
    }
  }
};

export default commitService; 