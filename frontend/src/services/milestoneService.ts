import axios from 'axios';

const API_BASE_URL = '/api/v1';

/**
 * Service for interacting with milestone API endpoints
 */
const milestoneService = {
  /**
   * Get all milestones
   * @param repositoryId Optional repository ID to filter by
   * @param isCompleted Optional completion status to filter by
   * @param dueBefore Optional due date upper bound
   * @param dueAfter Optional due date lower bound
   * @param includeDeleted Whether to include soft-deleted milestones
   * @returns Promise with milestones
   */
  getAllMilestones: async (
    repositoryId?: number,
    isCompleted?: boolean,
    dueBefore?: string,
    dueAfter?: string,
    includeDeleted: boolean = false
  ) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/milestones`, {
        params: {
          repository_id: repositoryId,
          is_completed: isCompleted,
          due_before: dueBefore,
          due_after: dueAfter,
          include_deleted: includeDeleted
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching milestones:', error);
      throw error;
    }
  },

  /**
   * Get milestone by ID
   * @param id Milestone ID
   * @param includeDeleted Whether to include soft-deleted milestones
   * @returns Promise with milestone details
   */
  getMilestoneById: async (id: number, includeDeleted: boolean = false) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/milestones/${id}`, {
        params: { include_deleted: includeDeleted }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching milestone with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new milestone
   * @param milestoneData Milestone data
   * @returns Promise with created milestone
   */
  createMilestone: async (milestoneData: any) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/milestones`, milestoneData);
      return response.data;
    } catch (error) {
      console.error('Error creating milestone:', error);
      throw error;
    }
  },

  /**
   * Update a milestone
   * @param id Milestone ID
   * @param milestoneData Milestone data to update
   * @returns Promise with updated milestone
   */
  updateMilestone: async (id: number, milestoneData: any) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/milestones/${id}`, milestoneData);
      return response.data;
    } catch (error) {
      console.error(`Error updating milestone with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a milestone
   * @param id Milestone ID
   * @param permanent Whether to permanently delete
   * @returns Promise with deletion result
   */
  deleteMilestone: async (id: number, permanent: boolean = false) => {
    try {
      const response = await axios.delete(`${API_BASE_URL}/milestones/${id}`, {
        params: { permanent }
      });
      return response.data;
    } catch (error) {
      console.error(`Error deleting milestone with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Restore a soft-deleted milestone
   * @param id Milestone ID
   * @returns Promise with restored milestone
   */
  restoreMilestone: async (id: number) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/milestones/${id}/restore`);
      return response.data;
    } catch (error) {
      console.error(`Error restoring milestone with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Mark a milestone as complete
   * @param id Milestone ID
   * @returns Promise with completed milestone
   */
  completeMilestone: async (id: number) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/milestones/${id}/complete`);
      return response.data;
    } catch (error) {
      console.error(`Error completing milestone with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Reopen a completed milestone
   * @param id Milestone ID
   * @returns Promise with reopened milestone
   */
  reopenMilestone: async (id: number) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/milestones/${id}/reopen`);
      return response.data;
    } catch (error) {
      console.error(`Error reopening milestone with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get milestone statistics
   * @param repositoryId Optional repository ID to filter by
   * @returns Promise with milestone statistics
   */
  getMilestoneStats: async (repositoryId?: number) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/milestones/stats`, {
        params: { repository_id: repositoryId }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching milestone statistics:', error);
      throw error;
    }
  },

  /**
   * Get milestone analytics data
   * @param repositoryId Repository ID
   * @returns Promise with milestone analytics data
   */
  getMilestoneAnalytics: async (repositoryId: number) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/milestones/analytics`, {
        params: { repository_id: repositoryId }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching milestone analytics for repository ${repositoryId}:`, error);
      throw error;
    }
  }
};

export default milestoneService; 