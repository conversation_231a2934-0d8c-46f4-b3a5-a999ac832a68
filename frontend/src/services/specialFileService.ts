import axios from 'axios';

const API_BASE_URL = '/api/v1';

/**
 * Service for interacting with special files API endpoints
 */
const specialFileService = {
  /**
   * Get all special files
   * @param repositoryId Optional repository ID to filter by
   * @param fileType Optional file type to filter by
   * @param includeDeleted Whether to include soft-deleted files
   * @returns Promise with special files
   */
  getAllSpecialFiles: async (
    repositoryId?: number,
    fileType?: string,
    includeDeleted: boolean = false
  ) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/special-files`, {
        params: {
          repository_id: repositoryId,
          file_type: fileType,
          include_deleted: includeDeleted
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching special files:', error);
      throw error;
    }
  },

  /**
   * Get special file by ID
   * @param id Special file ID
   * @param includeDeleted Whether to include soft-deleted files
   * @returns Promise with special file details
   */
  getSpecialFileById: async (id: number, includeDeleted: boolean = false) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/special-files/${id}`, {
        params: { include_deleted: includeDeleted }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching special file with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new special file
   * @param specialFileData Special file data
   * @returns Promise with created special file
   */
  createSpecialFile: async (specialFileData: any) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/special-files`, specialFileData);
      return response.data;
    } catch (error) {
      console.error('Error creating special file:', error);
      throw error;
    }
  },

  /**
   * Update a special file
   * @param id Special file ID
   * @param specialFileData Special file data to update
   * @returns Promise with updated special file
   */
  updateSpecialFile: async (id: number, specialFileData: any) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/special-files/${id}`, specialFileData);
      return response.data;
    } catch (error) {
      console.error(`Error updating special file with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a special file
   * @param id Special file ID
   * @param permanent Whether to permanently delete
   * @returns Promise with deletion result
   */
  deleteSpecialFile: async (id: number, permanent: boolean = false) => {
    try {
      const response = await axios.delete(`${API_BASE_URL}/special-files/${id}`, {
        params: { permanent }
      });
      return response.data;
    } catch (error) {
      console.error(`Error deleting special file with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Restore a soft-deleted special file
   * @param id Special file ID
   * @returns Promise with restored special file
   */
  restoreSpecialFile: async (id: number) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/special-files/${id}/restore`);
      return response.data;
    } catch (error) {
      console.error(`Error restoring special file with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Sync special files for a repository
   * @param repositoryId Repository ID
   * @returns Promise with synced special files
   */
  syncSpecialFiles: async (repositoryId: number) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/special-files/sync/${repositoryId}`);
      return response.data;
    } catch (error) {
      console.error(`Error syncing special files for repository ${repositoryId}:`, error);
      throw error;
    }
  },

  /**
   * Get count of special files
   * @param repositoryId Optional repository ID to filter by
   * @param fileType Optional file type to filter by
   * @returns Promise with count statistics
   */
  getSpecialFilesCount: async (repositoryId?: number, fileType?: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/special-files/count`, {
        params: {
          repository_id: repositoryId,
          file_type: fileType
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching special files count:', error);
      throw error;
    }
  }
};

export default specialFileService; 