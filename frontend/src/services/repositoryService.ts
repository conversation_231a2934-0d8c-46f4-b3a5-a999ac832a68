import axios from 'axios';

const API_BASE_URL = '/api/v1';

/**
 * Service for interacting with repository-related API endpoints
 */
const repositoryService = {
  /**
   * Get all repositories
   * @param includeDeleted Whether to include soft-deleted repositories
   * @returns Promise with repositories
   */
  getAllRepositories: async (includeDeleted: boolean = false) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/repositories`, {
        params: { include_deleted: includeDeleted }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching repositories:', error);
      throw error;
    }
  },

  /**
   * Get only deleted repositories
   * @returns Promise with deleted repositories
   */
  getDeletedRepositories: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/repositories/deleted`);
      return response.data;
    } catch (error) {
      console.error('Error fetching deleted repositories:', error);
      throw error;
    }
  },

  /**
   * Get repository by ID
   * @param id Repository ID
   * @param includeDeleted Whether to include soft-deleted repositories
   * @returns Promise with repository details
   */
  getRepositoryById: async (id: number, includeDeleted: boolean = false) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/repositories/${id}`, {
        params: { include_deleted: includeDeleted }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching repository with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new repository
   * @param repositoryData Repository data
   * @returns Promise with created repository
   */
  createRepository: async (repositoryData: any) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/repositories`, repositoryData);
      return response.data;
    } catch (error) {
      console.error('Error creating repository:', error);
      throw error;
    }
  },

  /**
   * Update a repository
   * @param id Repository ID
   * @param repositoryData Repository data to update
   * @returns Promise with updated repository
   */
  updateRepository: async (id: number, repositoryData: any) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/repositories/${id}`, repositoryData);
      return response.data;
    } catch (error) {
      console.error(`Error updating repository with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a repository
   * @param id Repository ID
   * @param permanent Whether to permanently delete
   * @returns Promise with deletion result
   */
  deleteRepository: async (id: number, permanent: boolean = false) => {
    try {
      const response = await axios.delete(`${API_BASE_URL}/repositories/${id}`, {
        params: { permanent }
      });
      return response.data;
    } catch (error) {
      console.error(`Error deleting repository with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Restore a soft-deleted repository
   * @param id Repository ID
   * @returns Promise with restored repository
   */
  restoreRepository: async (id: number) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/repositories/${id}/restore`);
      return response.data;
    } catch (error) {
      console.error(`Error restoring repository with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Sync a repository with GitHub
   * @param id Repository ID
   * @returns Promise with sync result
   */
  syncRepository: async (id: number) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/repositories/${id}/sync`);
      return response.data;
    } catch (error) {
      console.error(`Error syncing repository with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get file type analytics for a repository
   * @param id Repository ID
   * @returns Promise with file type statistics
   */
  getFileTypeAnalytics: async (id: number) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/repositories/${id}/analytics/files`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching file type analytics for repository ${id}:`, error);
      throw error;
    }
  },

  /**
   * Export repository data
   * @param id Repository ID
   * @param format Export format ('csv' or 'pdf')
   * @returns Promise with export result
   */
  exportRepositoryData: async (id: number, format: 'csv' | 'pdf') => {
    try {
      const response = await axios.get(`${API_BASE_URL}/repositories/${id}/export`, {
        params: { format },
        responseType: 'blob'
      });
      
      // Create a download link and trigger it
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `repository-${id}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      return true;
    } catch (error) {
      console.error(`Error exporting repository ${id} as ${format}:`, error);
      throw error;
    }
  }
};

export default repositoryService; 