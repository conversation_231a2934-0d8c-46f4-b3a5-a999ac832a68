import axios from 'axios';

const API_BASE_URL = '/api/v1';

/**
 * Service for interacting with GitHub-related API endpoints
 */
const githubService = {
  /**
   * Search for GitHub repositories
   * @param query Search query string
   * @returns Promise with search results
   */
  searchRepositories: async (query: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/github/search`, {
        params: { q: query }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching GitHub repositories:', error);
      throw error;
    }
  },

  /**
   * Get repository details from GitHub
   * @param owner Repository owner
   * @param repo Repository name
   * @returns Promise with repository details
   */
  getRepositoryDetails: async (owner: string, repo: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/github/repos/${owner}/${repo}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching repository details:', error);
      throw error;
    }
  },

  /**
   * Get user's GitHub repositories
   * @returns Promise with user repositories
   */
  getUserRepositories: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/github/user/repos`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user repositories:', error);
      throw error;
    }
  }
};

export default githubService; 