import axios from 'axios';

const API_BASE_URL = '/api/v1';

/**
 * Service for interacting with user-related API endpoints
 */
const userService = {
  /**
   * Get current user profile
   * @returns Promise with user profile
   */
  getCurrentUser: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/me`);
      return response.data;
    } catch (error) {
      console.error('Error fetching current user:', error);
      throw error;
    }
  },

  /**
   * Update user profile
   * @param userData User data to update
   * @returns Promise with updated user profile
   */
  updateUserProfile: async (userData: any) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/me`, userData);
      return response.data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },

  /**
   * Get user settings
   * @returns Promise with user settings
   */
  getUserSettings: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/me/settings`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user settings:', error);
      throw error;
    }
  },

  /**
   * Update user settings
   * @param settingsData Settings data to update
   * @returns Promise with updated user settings
   */
  updateUserSettings: async (settingsData: any) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/me/settings`, settingsData);
      return response.data;
    } catch (error) {
      console.error('Error updating user settings:', error);
      throw error;
    }
  },

  /**
   * Get user API keys
   * @returns Promise with user API keys
   */
  getUserApiKeys: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/me/api-keys`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user API keys:', error);
      throw error;
    }
  },

  /**
   * Generate new API key
   * @param description Optional description for the API key
   * @returns Promise with new API key
   */
  generateApiKey: async (description?: string) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/users/me/api-keys`, { description });
      return response.data;
    } catch (error) {
      console.error('Error generating API key:', error);
      throw error;
    }
  },

  /**
   * Revoke API key
   * @param keyId API key ID to revoke
   * @returns Promise with revocation result
   */
  revokeApiKey: async (keyId: string) => {
    try {
      const response = await axios.delete(`${API_BASE_URL}/users/me/api-keys/${keyId}`);
      return response.data;
    } catch (error) {
      console.error(`Error revoking API key ${keyId}:`, error);
      throw error;
    }
  },

  /**
   * Get user notification preferences
   * @returns Promise with notification preferences
   */
  getNotificationPreferences: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/me/notifications/preferences`);
      return response.data;
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      throw error;
    }
  },

  /**
   * Update notification preferences
   * @param preferencesData Preferences data to update
   * @returns Promise with updated notification preferences
   */
  updateNotificationPreferences: async (preferencesData: any) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/me/notifications/preferences`, preferencesData);
      return response.data;
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }
};

export default userService; 