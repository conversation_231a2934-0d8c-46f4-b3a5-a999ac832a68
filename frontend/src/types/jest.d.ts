// Type definitions for Jest
import '@testing-library/jest-dom';

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveTextContent(text: string): R;
      toHaveAttribute(attr: string, value?: string): R;
      toHaveClass(className: string): R;
      toHaveStyle(style: Record<string, any>): R;
      toBeVisible(): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toBeChecked(): R;
      toBeEmpty(): R;
      toBeInvalid(): R;
      toBeRequired(): R;
      toBeValid(): R;
      toContainElement(element: HTMLElement | null): R;
      toContainHTML(htmlText: string): R;
      toHaveFocus(): R;
      toHaveFormValues(expectedValues: Record<string, any>): R;
      toHaveValue(value: string | string[] | number): R;
      toBeInTheDOM(): R;
      toHaveDescription(text: string): R;
    }
  }
}

// Extend window with Jest mock functions
interface Window {
  matchMedia: jest.Mock;
  scrollTo: jest.Mock;
  IntersectionObserver: {
    new (callback: IntersectionObserverCallback): {
      observe: jest.Mock;
      unobserve: jest.Mock;
      disconnect: jest.Mock;
      callback: IntersectionObserverCallback;
    };
  };
  ResizeObserver: {
    new (callback: ResizeObserverCallback): {
      observe: jest.Mock;
      unobserve: jest.Mock;
      disconnect: jest.Mock;
      callback: ResizeObserverCallback;
    };
  };
}

// Extend URL with Jest mock functions
interface URL {
  createObjectURL: jest.Mock;
  revokeObjectURL: jest.Mock;
} 