# RepoTimeline

A comprehensive platform for tracking and visualizing GitHub repository timelines, special configuration files, and project milestones.

## Features

### Repository Management
- **Repository Tracking**: Add and track GitHub repositories
- **Repository Synchronization**: Sync repositories with GitHub to get the latest data
- **Soft Delete**: Move repositories to trash instead of permanent deletion
- **Restoration**: Restore repositories from trash when needed

### Special Files Tracking
- **Configuration Files**: Track special configuration files (.dockerwrapper, .claude, .cursor)
- **File Content**: View and edit file content with syntax highlighting
- **File History**: Track changes to special files over time

### Commit Tracking
- **Commit History**: View repository commit history
- **Author Information**: Track commit authors and their contributions
- **Commit Details**: View detailed commit information including messages and timestamps

### Milestone Management
- **Project Milestones**: Create and track project milestones
- **Due Dates**: Set and monitor milestone due dates
- **Completion Status**: Mark milestones as complete or reopen them
- **Progress Tracking**: Monitor milestone completion progress

### Analytics and Reporting
- **Commit Analytics**: Visualize commit frequency and patterns
- **Author Contributions**: See distribution of commits across contributors
- **Milestone Status**: View milestone completion rates and status
- **Special Files Analysis**: Analyze distribution of special configuration files
- **Data Export**: Export repository data in CSV or PDF formats

### User Management
- **User Profiles**: Manage user profile information
- **Settings**: Configure application settings and preferences
- **API Keys**: Generate and manage API keys for programmatic access
- **Notification Preferences**: Configure how and when to receive notifications

### Security
- **GitHub OAuth**: Secure authentication with GitHub
- **Token Management**: Secure handling of GitHub access tokens
- **Role-based Access**: Control access to repositories and features

## Getting Started

### Prerequisites
- Node.js (v14+)
- PostgreSQL (v12+)
- GitHub account
- Docker and Docker Compose (for containerized setup)

### Installation

#### Standard Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/repotimeline.git
cd repotimeline
```

2. Install dependencies:
```bash
# Install backend dependencies
cd backend
pip install -r requirements.txt

# Install frontend dependencies
cd ../frontend
npm install
```

3. Configure environment variables:
```bash
# Backend (.env)
DATABASE_URL=postgresql://user:password@localhost:5432/repotimeline
SECRET_KEY=your_secret_key
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Frontend (.env)
REACT_APP_API_URL=http://localhost:8000/api
```

4. Run database migrations:
```bash
cd backend
alembic upgrade head
```

5. Start the development servers:
```bash
# Backend
cd backend
uvicorn app.main:app --reload --port 8000

# Frontend
cd frontend
npm start
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

#### Docker Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/repotimeline.git
cd repotimeline
```

2. Configure environment variables in `.env` file:
```
DATABASE_URL=**************************************/repotimeline
SECRET_KEY=your_secret_key
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
```

3. Start the Docker containers:
```bash
cd .dockerwrapper
docker-compose up -d
```

4. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost/api
   - API Documentation: http://localhost/docs

## Testing

RepoTimeline includes comprehensive test suites for both backend and frontend to ensure code quality and reliability.

### Backend Tests

The backend tests are organized into unit tests and integration tests using pytest.

```bash
# Run all backend tests
cd backend
pytest

# Run with coverage report
pytest --cov=app

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
```

### Frontend Tests

The frontend tests use Jest and React Testing Library to test components and services.

```bash
# Run all frontend tests
cd frontend
npm test

# Run tests in watch mode (for development)
npm run test:watch

# Run with coverage report
npm run test:coverage
```

For more details on the frontend test suite, see [frontend/src/__tests__/README.md](frontend/src/__tests__/README.md).

## Usage

### Adding a Repository
1. Log in with your GitHub account
2. Navigate to the Dashboard
3. Click "Add Repository"
4. Search for a GitHub repository
5. Select the repository and click "Add"

### Tracking Special Files
1. Navigate to a repository detail page
2. Go to the "Special Files" tab
3. View and manage special configuration files
4. Click "Sync Files" to fetch the latest files from GitHub

### Managing Milestones
1. Navigate to a repository detail page
2. Go to the "Milestones" tab
3. Create new milestones or manage existing ones
4. Set due dates and track completion status

### Viewing Analytics
1. Navigate to a repository detail page
2. Click on "Analytics" in the repository header
3. View commit frequency, author contributions, and milestone status
4. Use date filters to analyze specific time periods
5. Export data in CSV or PDF format

## API Documentation

RepoTimeline provides a comprehensive API for programmatic access to all features. For detailed API documentation, see [docs/api.md](docs/api.md).

### Authentication
```
POST /api/v1/auth/token
```
Obtain an access token using your API key.

### Repositories
```
GET /api/v1/repositories
POST /api/v1/repositories
GET /api/v1/repositories/{id}
PUT /api/v1/repositories/{id}
DELETE /api/v1/repositories/{id}
POST /api/v1/repositories/{id}/restore
POST /api/v1/repositories/{id}/sync
```

### Special Files
```
GET /api/v1/special-files
POST /api/v1/special-files
GET /api/v1/special-files/{id}
PUT /api/v1/special-files/{id}
DELETE /api/v1/special-files/{id}
POST /api/v1/special-files/{id}/restore
POST /api/v1/special-files/sync/{repository_id}
```

### Commits
```
GET /api/v1/commits
POST /api/v1/commits
GET /api/v1/commits/{id}
PUT /api/v1/commits/{id}
DELETE /api/v1/commits/{id}
POST /api/v1/commits/{id}/restore
POST /api/v1/commits/sync/{repository_id}
```

### Milestones
```
GET /api/v1/milestones
POST /api/v1/milestones
GET /api/v1/milestones/{id}
PUT /api/v1/milestones/{id}
DELETE /api/v1/milestones/{id}
POST /api/v1/milestones/{id}/restore
POST /api/v1/milestones/{id}/complete
POST /api/v1/milestones/{id}/reopen
```

### Analytics
```
GET /api/v1/repositories/{id}/analytics/files
GET /api/v1/commits/analytics
GET /api/v1/milestones/analytics
GET /api/v1/repositories/{id}/export
```

## Security Considerations

### GitHub Token Handling
- Tokens are encrypted before storage using AES-256
- All GitHub API requests are made server-side
- Tokens are never exposed to the frontend
- Users can revoke access at any time

### Data Protection
- Soft delete functionality prevents accidental data loss
- Regular backups of the database
- HTTPS enforced for all communications
- Secure cookie handling with HttpOnly and Secure flags

## Roadmap

### Upcoming Features
- Team management and collaboration
- GitLab and Bitbucket integration
- CI/CD pipeline tracking
- Branch management and comparison
- Code review tracking
- Repository health metrics
- Mobile application

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow the coding style and conventions used in the project
- Write tests for new features and bug fixes
- Update documentation for any changes to the API or functionality
- Run the test suite before submitting a pull request
- Keep pull requests focused on a single feature or bug fix

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements
- [FastAPI](https://fastapi.tiangolo.com/)
- [React](https://reactjs.org/)
- [Ant Design](https://ant.design/)
- [Recharts](https://recharts.org/)
- [GitHub API](https://docs.github.com/en/rest) 