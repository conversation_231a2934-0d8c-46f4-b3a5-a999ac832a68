---
description: 
globs: **/*.py
alwaysApply: false
---
---
name: PEP 257 Docstring Conventions
version: "1.0"
globs:
  - "**/*.py"
triggers:
  - file_change
  - file_open
  - code_action
---
```yaml
rule_definition:
  description: "Enforces PEP 257 docstring conventions"
  patterns:
    - name: "missing_module_docstring"
      pattern: "^import .*\\n\\n(class|def)"
      advice: "PEP 257: Missing module level docstring at the beginning of file"
    
    - name: "missing_class_docstring"
      pattern: "class [A-Za-z0-9_]+\\(.*\\):\\n( {4}[^'\"]|\\n)"
      advice: "PEP 257: Missing class docstring"
    
    - name: "missing_function_docstring"
      pattern: "def [A-Za-z0-9_]+\\(.*\\):\\n( {4}[^'\"]|\\n)"
      advice: "PEP 257: Missing function docstring"
    
    - name: "one_line_docstring_multiline"
      pattern: "\"\"\".*\\n.*\"\"\""
      advice: "PEP 257: One-line docstrings should be on a single line"
    
    - name: "docstring_starts_with_space"
      pattern: "\"\"\" "
      advice: "PEP 257: Docstring should not start with a space"
    
    - name: "docstring_ends_with_space"
      pattern: " \"\"\""
      advice: "PEP 257: Docstring should not end with a space"
    
    - name: "multiline_docstring_summary"
      pattern: "\"\"\".*\\n\\n {4}[a-z]"
      advice: "PEP 257: First line of multiline docstring should be a summary line, followed by a blank line"
```