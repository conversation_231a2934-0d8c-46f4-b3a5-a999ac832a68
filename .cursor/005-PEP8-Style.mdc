---
description: 
globs: **/*.py
alwaysApply: false
---
---
name: PEP 8 Style Guidelines
version: "1.0"
globs:
  - "**/*.py"
triggers:
  - file_change
  - file_open
  - code_action
---
```yaml
rule_definition:
  description: "Enforces PEP 8 style guidelines for Python code"
  patterns:
    - name: "line_length"
      pattern: ".{80,}"
      advice: "PEP 8: Line too long (should be 79 characters or less for code, 72 for docstrings/comments)"
    
    - name: "indentation"
      pattern: "^( {1,3}|[^ ].*\\t)"
      advice: "PEP 8: Use 4 spaces for indentation (not tabs or inconsistent spacing)"
    
    - name: "import_ordering"
      pattern: "import (?!sys|os|re|typing)[a-z].*\\nimport (sys|os|re|typing)"
      advice: "PEP 8: Imports should be grouped in the following order: standard library, related third party, local application/library"
    
    - name: "class_names"
      pattern: "class [a-z]"
      advice: "PEP 8: Class names should use CamelCase convention"
    
    - name: "function_names"
      pattern: "def [A-Z]"
      advice: "PEP 8: Function names should use snake_case convention"
    
    - name: "variable_names"
      pattern: "([A-Z][a-z]+){2,} ="
      advice: "PEP 8: Variable names should generally use snake_case convention, not CamelCase"
    
    - name: "constant_names"
      pattern: "^[a-z]+ = ['\"]?[^'\"]*['\"]?$"
      advice: "PEP 8: Constants (defined at module level) should use ALL_CAPS"
    
    - name: "whitespace_before_inline_comment"
      pattern: "[^ ]#"
      advice: "PEP 8: Use at least two spaces before inline comments"
    
    - name: "blank_lines_between_functions"
      pattern: "def .*:\\n( {4}.*\\n)+ {0,3}def"
      advice: "PEP 8: Use two blank lines between top-level functions and class definitions"
```