---
description: 
globs: 
alwaysApply: false
---
 ---
name: Best Practices
version: "1.0"
globs:
  - "api/**/*.py"
triggers:
  - file_change
  - code_action
---
```yaml
rule_definition:
  description: "Rules for best practices in the combined application"
  patterns:
    - name: "proper_logging"
      pattern: "logger\\.(debug|info|warning|error|critical)\\("
      advice: "Ensure proper logging for better debugging and monitoring."
    
    - name: "middleware_order"
      pattern: "app\\.add_middleware\\(CORSMiddleware"
      advice: "BEST PRACTICE: CORS middleware should typically be one of the first middleware added."
    
    - name: "dependency_injection"
      pattern: "Depends\\("
      advice: "BEST PRACTICE: Use dependency injection for reusable components and better testing."
    
    - name: "async_def"
      pattern: "async def"
      advice: "BEST PRACTICE: FastAPI routes should be async for better performance unless they perform blocking I/O."
    
    - name: "pydantic_models"
      pattern: "class .*(Schema|Model)\\(BaseModel\\):"
      advice: "BEST PRACTICE: Use Pydantic models for request validation and response serialization."
```