# Mermaid Diagram Rules for RepoTimeline

## When to Use Mermaid
- Always use Mermaid diagrams in Markdown files when visualizing:
  - Architecture diagrams
  - Workflow processes
  - Entity relationships
  - Component hierarchies
  - Sequence diagrams
  - Gantt charts for timelines
  - State diagrams

## Mermaid Syntax
- Always use the triple backtick syntax with the mermaid language specifier:
  ```
  ```mermaid
  // diagram code here
  ```
  ```

## Diagram Types and Usage

### Flow Charts
Use for visualizing processes, workflows, and decision trees:
```mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
```

### Entity Relationship Diagrams
Use for database schema visualization:
```mermaid
erDiagram
    ENTITY1 ||--o{ ENTITY2 : relationship
    ENTITY1 {
        string attribute1
        number attribute2
    }
    ENTITY2 {
        string attribute1
        number attribute2
    }
```

### Sequence Diagrams
Use for API interactions and service communications:
```mermaid
sequenceDiagram
    participant Client
    participant Server
    Client->>Server: Request
    Server->>Client: Response
```

### Gantt Charts
Use for project timelines and milestones:
```mermaid
gantt
    title Project Timeline
    dateFormat  YYYY-MM-DD
    section Phase 1
    Task 1           :a1, 2023-01-01, 30d
    Task 2           :after a1, 20d
```

### Class Diagrams
Use for code architecture visualization:
```mermaid
classDiagram
    Class01 <|-- AveryLongClass : Inheritance
    Class03 *-- Class04 : Composition
    Class05 o-- Class06 : Aggregation
```

## Styling Guidelines
- Use consistent node shapes across similar diagrams
- Use descriptive labels for nodes and edges
- Keep diagrams focused and not overly complex
- Use colors sparingly and consistently
- Include a title or caption for each diagram

## Accessibility Considerations
- Ensure diagrams have sufficient contrast
- Provide text descriptions for complex diagrams
- Use patterns in addition to colors for differentiation
- Keep text readable (not too small)

## Project-Specific Diagram Templates

### Repository Timeline
```mermaid
gantt
    title Repository Timeline
    dateFormat  YYYY-MM-DD
    section Features
    Feature 1           :done, f1, 2023-01-01, 15d
    Feature 2           :done, f2, after f1, 15d
    Feature 3           :active, f3, after f2, 30d
    Feature 4           :f4, after f3, 30d
```

### Repository Architecture
```mermaid
graph TD
    A[Frontend] --> B[API Layer]
    B --> C[Service Layer]
    C --> D[Database]
    C --> E[External APIs]
```

### Database Schema
```mermaid
erDiagram
    REPOSITORIES ||--o{ SPECIAL_FILES : contains
    REPOSITORIES ||--o{ COMMITS : has
    REPOSITORIES ||--o{ MILESTONES : tracks
```

### API Flow
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant GitHub
    participant DB
    Client->>API: Get Repository Data
    API->>GitHub: Fetch Repository
    GitHub->>API: Repository Data
    API->>DB: Store Data
    API->>Client: Return Data
``` 