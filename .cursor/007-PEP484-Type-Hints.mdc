---
description: 
globs: **/*.py
alwaysApply: false
---
# 007-PEP484-Type-Hints.mdc
---
name: PEP 484 Type Hints
version: "1.0"
globs:
  - "**/*.py"
triggers:
  - file_change
  - file_open
  - code_action
---
```yaml
rule_definition:
  description: "Enforces PEP 484 type hint conventions"
  patterns:
    - name: "missing_return_type"
      pattern: "def [A-Za-z0-9_]+\\(.*\\)(:|\\n)"
      advice: "PEP 484: Consider adding return type annotation (-> ReturnType)"
    
    - name: "missing_parameter_types"
      pattern: "def [A-Za-z0-9_]+\\(([^:]*(, )?)+\\)"
      advice: "PEP 484: Consider adding type annotations for parameters (param: Type)"
    
    - name: "any_type_usage"
      pattern: "from typing import Any|: Any"
      advice: "PEP 484: Using Any defeats the purpose of type checking. Consider a more specific type if possible."
    
    - name: "typed_dict_legacy"
      pattern: "class .*\\(TypedDict\\)"
      advice: "PEP 484: Consider using the more modern TypedDict syntax with type annotation"
    
    - name: "optional_parameter"
      pattern: "= None(,|\\))"
      advice: "PEP 484: Parameters with default None should use Optional[Type] annotation"
    
    - name: "missing_generic_types"
      pattern: ": (List|Dict|Tuple|Set|FrozenSet)\\]"
      advice: "PEP 484: Generic container types should specify contained types (e.g., List[str], Dict[str, int])"
    
    - name: "union_types"
      pattern: "Union\\["
      advice: "PEP 484: In Python 3.10+, consider using the | operator instead of Union (e.g., str | int)"
    
    - name: "collection_type_hints"
      pattern: ": list\\b|: dict\\b|: tuple\\b|: set\\b"
      advice: "PEP 484: Use typing collection types (List, Dict, Tuple, Set) instead of builtin types for annotations"
```