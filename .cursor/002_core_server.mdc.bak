---
description: 
globs: 
alwaysApply: false
---
---
name: Core Server Components
version: "1.0"
globs:
  - "api/application.py"
  - "main.py"
triggers:
  - file_change
  - file_open
---
```yaml
rule_definition:
  description: "Rules for the combined FastAPI and Flask application"
  patterns:
    - name: "flask_app_definition"
      pattern: "flask_app = Flask\\(__name__\\)"
      advice: "This defines the Flask application that will be mounted under FastAPI at the '/ui' path."
    
    - name: "fastapi_app_definition"
      pattern: "app = FastAPI\\(.*\\)"
      advice: "Main FastAPI application that handles API endpoints and mounts the Flask UI."
    
    - name: "flask_app_mounting"
      pattern: "app\\.mount\\(\"/ui\", WSGIMiddleware\\(flask_app\\)\\)"
      advice: "This mounts the Flask application under the '/ui' path in FastAPI. All Flask routes will be prefixed with '/ui'."
    
    - name: "fastapi_middlewares"
      pattern: "app\\.add_middleware\\("
      advice: "Middleware configuration for FastAPI. Note that this won't affect the Flask application."
    
    - name: "fastapi_include_router"
      pattern: "app\\.include_router\\("
      advice: "Registers API route handlers with FastAPI."
```