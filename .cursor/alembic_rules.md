# Alembic Migration Rules for RepoTimeline

This document outlines the rules and best practices for using Alembic to manage database migrations in the RepoTimeline project.

## Migration Workflow

```mermaid
graph TD
    A[Modify SQLAlchemy Models] --> B[Generate Migration]
    B --> C[Review Migration]
    C --> D[Apply Migration]
    D --> E[Commit Migration]
    E --> F[Deploy]
```

## General Rules

1. **Always use Alembic for schema changes**
   - Never modify the database schema directly
   - All changes must be tracked through migration scripts

2. **One change per migration**
   - Each migration should represent a single logical change
   - Avoid combining multiple unrelated changes in one migration

3. **Descriptive migration names**
   - Use descriptive names for migration scripts
   - Format: `YYYYMMDD_short_description.py`
   - Example: `20230215_add_soft_delete_to_models.py`

4. **Test migrations before committing**
   - Test both upgrade and downgrade paths
   - Verify data integrity after migration

5. **Include comments in migration scripts**
   - Document the purpose of each migration
   - Explain complex operations or dependencies

## Creating Migrations

### Automatic Generation

```bash
# Generate a new migration with automatic detection
alembic revision --autogenerate -m "description_of_changes"
```

### Manual Creation

```bash
# Create a new empty migration
alembic revision -m "description_of_changes"
```

## Applying Migrations

### Upgrade Database

```bash
# Upgrade to the latest version
alembic upgrade head

# Upgrade to a specific version
alembic upgrade <revision>

# Upgrade by a relative number of revisions
alembic upgrade +1
```

### Downgrade Database

```bash
# Downgrade to the previous version
alembic downgrade -1

# Downgrade to a specific version
alembic downgrade <revision>

# Downgrade to base (remove all migrations)
alembic downgrade base
```

## Best Practices

### Soft Delete Implementation

All models should inherit from the `BaseModel` class which provides soft delete functionality:

```python
# Example model with soft delete
class MyModel(BaseModel):
    __tablename__ = "my_models"
    
    id = Column(Integer, primary_key=True)
    name = Column(String)
    # Other fields...
    
    # BaseModel provides:
    # - created_at
    # - updated_at
    # - deleted_at
    # - is_deleted
```

### Migration Script Structure

```python
"""Description of migration

Revision ID: abcdef123456
Revises: 98765fedcba
Create Date: 2023-02-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers
revision = 'abcdef123456'
down_revision = '98765fedcba'
branch_labels = None
depends_on = None


def upgrade():
    # Implementation of upgrade operations
    # Example:
    op.add_column('my_table', sa.Column('new_column', sa.String(), nullable=True))
    
    # For adding soft delete to existing tables:
    op.add_column('my_table', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('my_table', sa.Column('deleted_at', sa.DateTime(), nullable=True))


def downgrade():
    # Implementation of downgrade operations
    # Example:
    op.drop_column('my_table', 'new_column')
    
    # For removing soft delete from tables:
    op.drop_column('my_table', 'deleted_at')
    op.drop_column('my_table', 'is_deleted')
```

### Data Migrations

When performing data migrations:

1. Use raw SQL for complex operations
2. Batch process large datasets to avoid timeouts
3. Include data validation before and after migration

```python
def upgrade():
    # Example of data migration
    connection = op.get_bind()
    
    # Execute raw SQL
    connection.execute(
        """
        UPDATE users
        SET full_name = first_name || ' ' || last_name
        WHERE full_name IS NULL
        """
    )
```

## Common Migration Patterns

### Adding a New Table

```python
def upgrade():
    op.create_table(
        'new_table',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), onupdate=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_new_table_id'), 'new_table', ['id'], unique=False)

def downgrade():
    op.drop_index(op.f('ix_new_table_id'), table_name='new_table')
    op.drop_table('new_table')
```

### Adding a Foreign Key

```python
def upgrade():
    op.add_column('child_table', sa.Column('parent_id', sa.Integer(), nullable=True))
    op.create_foreign_key(
        'fk_child_parent',
        'child_table', 'parent_table',
        ['parent_id'], ['id'],
        ondelete='CASCADE'
    )

def downgrade():
    op.drop_constraint('fk_child_parent', 'child_table', type_='foreignkey')
    op.drop_column('child_table', 'parent_id')
```

### Modifying Column Properties

```python
def upgrade():
    # Make a nullable column non-nullable with a default value
    op.alter_column('my_table', 'my_column',
               existing_type=sa.String(),
               nullable=False,
               server_default='default_value')

def downgrade():
    # Revert to nullable without default
    op.alter_column('my_table', 'my_column',
               existing_type=sa.String(),
               nullable=True,
               server_default=None)
```

## Troubleshooting

### Migration Conflicts

If you encounter migration conflicts:

1. Identify the conflicting migrations
2. Create a new migration that resolves the conflict
3. Use `depends_on` to specify dependencies

### Failed Migrations

If a migration fails:

1. Fix the issue in the migration script
2. Run `alembic upgrade head` again
3. If necessary, manually fix the database and mark the migration as complete

```bash
# Mark a migration as complete without running it
alembic stamp <revision>
``` 